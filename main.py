# Головний файл гри Agar.io Clone з ШІ-агентами
import pygame
import time
import sys
from config import *
from environment import Environment
from evolution import EvolutionManager
from visualization import Visualizer

class Game:
    """Головний клас гри."""

    def __init__(self):
        # Ініціалізуємо компоненти
        self.environment = Environment()
        self.evolution_manager = EvolutionManager()
        self.visualizer = Visualizer()

        # Стан гри
        self.running = True
        self.clock = pygame.time.Clock()
        self.last_time = time.time()

        # Ініціалізуємо першу популяцію
        self._initialize_first_generation()

        print("Agar.io Clone з ШІ-агентами запущено!")
        print("Керування:")
        print("  R - Перезапуск покоління")
        print("  V - Увімкнути/вимкнути візуалізацію променів зору")
        print("  P - Пауза/продовження")
        print("  C - Переключення режиму камери (авто/вільна)")
        print("  WASD або стрілки - Рух вільної камери")
        print("  ESC - Вихід")
        print("  Колесо миші - Зум камери")
        print()
        print("💡 Порада: Гра починається з вільною камерою.")
        print("   Використовуйте WASD для переміщення та спостереження за агентами!")

    def _initialize_first_generation(self):
        """Ініціалізує перше покоління."""
        # Отримуємо стартові гени
        starter_genes = self.evolution_manager.get_starter_genes(POPULATION_SIZE)

        # Створюємо популяцію
        self.environment.create_initial_population(starter_genes)

        print(f"Створено покоління {self.environment.generation} з {len(self.environment.cells)} агентів")

        # Показуємо інформацію про еволюцію
        evolution_summary = self.evolution_manager.get_evolution_summary()
        if evolution_summary.get('has_saved_genes'):
            print(f"Завантажено збережені гени з фітнесом {evolution_summary['best_fitness_ever']:.2f}")

    def run(self):
        """Основний цикл гри."""
        while self.running:
            # Обчислюємо delta time
            current_time = time.time()
            dt = current_time - self.last_time
            self.last_time = current_time

            # Обмежуємо dt для стабільності
            dt = min(dt, 1.0 / 30.0)  # Максимум 30 FPS для фізики

            # Обробляємо події
            events = self.visualizer.handle_events(self.environment)

            if events['quit']:
                self.running = False
                break

            if events['confirm_restart']:
                self._restart_generation()
                continue

            if events['toggle_camera']:
                camera_mode = "вільну" if self.visualizer.camera.free_camera_mode else "автоматичну"
                print(f"Переключено на {camera_mode} камеру")

            # Оновлюємо гру якщо не на паузі
            if not self.visualizer.paused:
                self._update_game(dt)

                # Перевіряємо чи потрібно перезапустити покоління
                if self.environment.should_restart_generation():
                    self._evolve_to_next_generation()

            # Оновлюємо камеру
            self.visualizer.update_camera(self.environment, dt)

            # Рендерим
            self.visualizer.render(self.environment)

            # Обмежуємо FPS
            self.clock.tick(FPS)

        # Очищуємо ресурси
        self.cleanup()

    def _update_game(self, dt: float):
        """Оновлює стан гри."""
        # Оновлюємо середовище
        self.environment.update(dt)

    def _restart_generation(self):
        """Перезапускає поточне покоління."""
        print(f"\nПерезапуск покоління {self.environment.generation}...")

        # Зберігаємо статистику
        stats = self.environment.get_generation_stats()
        self.evolution_manager.save_generation_stats(stats)

        # Скидаємо середовище
        self.environment.reset_generation()

        # Створюємо нову популяцію з тими ж генами
        starter_genes = self.evolution_manager.get_starter_genes(POPULATION_SIZE)
        self.environment.create_initial_population(starter_genes)

        print(f"Покоління {self.environment.generation} перезапущено")

    def _evolve_to_next_generation(self):
        """Еволюціонує до наступного покоління."""
        current_stats = self.environment.get_generation_stats()
        alive_cells = self.environment.get_alive_cells()

        print(f"\nПокоління {self.environment.generation} завершено:")
        print(f"  Час: {current_stats['time']:.1f}с")
        print(f"  Живих агентів: {current_stats['alive_count']}")
        print(f"  Максимальна енергія: {current_stats['max_energy']:.1f}")
        print(f"  Поділи: {current_stats['total_splits']}")
        print(f"  Поглинання: {current_stats['total_absorptions']}")

        # Зберігаємо статистику
        self.evolution_manager.save_generation_stats(current_stats)

        # Створюємо наступне покоління
        new_genes = self.evolution_manager.create_next_generation(alive_cells)

        # Скидаємо середовище
        self.environment.reset_generation()

        # Створюємо нову популяцію
        self.environment.create_initial_population(new_genes)

        print(f"Покоління {self.environment.generation} створено")

        # Показуємо еволюційну статистику
        evolution_summary = self.evolution_manager.get_evolution_summary()
        if evolution_summary:
            print(f"Найкращий фітнес: {evolution_summary['best_fitness_ever']:.2f}")

            if 'energy_trend' in evolution_summary:
                trend = evolution_summary['energy_trend']
                trend_text = "↑" if trend > 0 else "↓" if trend < 0 else "→"
                print(f"Тренд енергії: {trend_text} {trend:+.1f}")

    def cleanup(self):
        """Очищує ресурси гри."""
        print("\nЗавершення гри...")

        # Зберігаємо фінальну статистику
        if self.environment.cells:
            final_stats = self.environment.get_generation_stats()
            self.evolution_manager.save_generation_stats(final_stats)

        # Показуємо підсумок еволюції
        evolution_summary = self.evolution_manager.get_evolution_summary()
        if evolution_summary:
            print(f"\nПідсумок еволюції:")
            print(f"  Всього поколінь: {evolution_summary['total_generations']}")
            print(f"  Найкращий фітнес: {evolution_summary['best_fitness_ever']:.2f}")

            # Експортуємо найкращі гени для аналізу
            best_genes_analysis = self.evolution_manager.export_best_genes_for_analysis()
            if best_genes_analysis:
                print(f"  Найкращі гени збережено в {GENES_FILE}")

        # Очищуємо візуалізацію
        self.visualizer.cleanup()

        print("Дякуємо за гру!")


def main():
    """Головна функція."""
    try:
        # Перевіряємо наявність pygame
        pygame.init()

        # Створюємо та запускаємо гру
        game = Game()
        game.run()

    except ImportError as e:
        print(f"Помилка імпорту: {e}")
        print("Переконайтеся, що pygame встановлено: pip install pygame")
        sys.exit(1)

    except Exception as e:
        print(f"Помилка гри: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
