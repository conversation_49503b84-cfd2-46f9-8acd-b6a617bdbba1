# Просторова сітка для оптимізації колізій
from typing import List, Set, Tuple, Dict, Any
from config import SPATIAL_GRID_SIZE, WORLD_WIDTH, WORLD_HEIGHT
import math

class SpatialGrid:
    """Просторова сітка для швидкого пошуку об'єктів поблизу."""
    
    def __init__(self, world_width: float, world_height: float, cell_size: float = SPATIAL_GRID_SIZE):
        self.world_width = world_width
        self.world_height = world_height
        self.cell_size = cell_size
        
        # Розміри сітки
        self.grid_width = math.ceil(world_width / cell_size)
        self.grid_height = math.ceil(world_height / cell_size)
        
        # Сітка - словник з координатами як ключами та списками об'єктів як значеннями
        self.grid: Dict[Tuple[int, int], List[Any]] = {}
        
        # Кеш для швидкого доступу до комірок об'єктів
        self.object_cells: Dict[Any, Set[Tuple[int, int]]] = {}
    
    def _get_grid_coords(self, x: float, y: float) -> Tuple[int, int]:
        """Отримує координати комірки сітки для заданої позиції."""
        grid_x = max(0, min(self.grid_width - 1, int(x / self.cell_size)))
        grid_y = max(0, min(self.grid_height - 1, int(y / self.cell_size)))
        return (grid_x, grid_y)
    
    def _get_affected_cells(self, x: float, y: float, radius: float) -> Set[Tuple[int, int]]:
        """Отримує всі комірки сітки, які торкаються об'єкта з заданим радіусом."""
        cells = set()
        
        # Розраховуємо межі
        min_x = max(0, x - radius)
        max_x = min(self.world_width, x + radius)
        min_y = max(0, y - radius)
        max_y = min(self.world_height, y + radius)
        
        # Отримуємо координати комірок
        min_grid_x = int(min_x / self.cell_size)
        max_grid_x = int(max_x / self.cell_size)
        min_grid_y = int(min_y / self.cell_size)
        max_grid_y = int(max_y / self.cell_size)
        
        # Обмежуємо в межах сітки
        min_grid_x = max(0, min_grid_x)
        max_grid_x = min(self.grid_width - 1, max_grid_x)
        min_grid_y = max(0, min_grid_y)
        max_grid_y = min(self.grid_height - 1, max_grid_y)
        
        # Додаємо всі торкнуті комірки
        for gx in range(min_grid_x, max_grid_x + 1):
            for gy in range(min_grid_y, max_grid_y + 1):
                cells.add((gx, gy))
        
        return cells
    
    def add_object(self, obj: Any, x: float, y: float, radius: float = 0):
        """Додає об'єкт до сітки."""
        # Видаляємо об'єкт з попередніх комірок
        self.remove_object(obj)
        
        # Отримуємо нові комірки
        affected_cells = self._get_affected_cells(x, y, radius)
        
        # Додаємо об'єкт до комірок
        for cell_coords in affected_cells:
            if cell_coords not in self.grid:
                self.grid[cell_coords] = []
            self.grid[cell_coords].append(obj)
        
        # Зберігаємо кеш
        self.object_cells[obj] = affected_cells
    
    def remove_object(self, obj: Any):
        """Видаляє об'єкт з сітки."""
        if obj in self.object_cells:
            # Видаляємо з усіх комірок
            for cell_coords in self.object_cells[obj]:
                if cell_coords in self.grid and obj in self.grid[cell_coords]:
                    self.grid[cell_coords].remove(obj)
                    
                    # Видаляємо порожні комірки
                    if not self.grid[cell_coords]:
                        del self.grid[cell_coords]
            
            # Видаляємо з кешу
            del self.object_cells[obj]
    
    def update_object(self, obj: Any, x: float, y: float, radius: float = 0):
        """Оновлює позицію об'єкта в сітці."""
        # Отримуємо нові комірки
        new_cells = self._get_affected_cells(x, y, radius)
        
        # Отримуємо старі комірки
        old_cells = self.object_cells.get(obj, set())
        
        # Якщо комірки не змінилися, нічого не робимо
        if new_cells == old_cells:
            return
        
        # Видаляємо з старих комірок
        for cell_coords in old_cells - new_cells:
            if cell_coords in self.grid and obj in self.grid[cell_coords]:
                self.grid[cell_coords].remove(obj)
                if not self.grid[cell_coords]:
                    del self.grid[cell_coords]
        
        # Додаємо до нових комірок
        for cell_coords in new_cells - old_cells:
            if cell_coords not in self.grid:
                self.grid[cell_coords] = []
            self.grid[cell_coords].append(obj)
        
        # Оновлюємо кеш
        self.object_cells[obj] = new_cells
    
    def get_nearby_objects(self, x: float, y: float, radius: float) -> List[Any]:
        """Отримує всі об'єкти поблизу заданої позиції."""
        nearby_objects = []
        affected_cells = self._get_affected_cells(x, y, radius)
        
        # Збираємо об'єкти з усіх торкнутих комірок
        seen_objects = set()
        for cell_coords in affected_cells:
            if cell_coords in self.grid:
                for obj in self.grid[cell_coords]:
                    if obj not in seen_objects:
                        nearby_objects.append(obj)
                        seen_objects.add(obj)
        
        return nearby_objects
    
    def clear(self):
        """Очищає всю сітку."""
        self.grid.clear()
        self.object_cells.clear()
    
    def get_stats(self) -> Dict[str, int]:
        """Повертає статистику сітки для діагностики."""
        total_objects = len(self.object_cells)
        used_cells = len(self.grid)
        total_cells = self.grid_width * self.grid_height
        
        # Розподіл об'єктів по комірках
        objects_per_cell = [len(objects) for objects in self.grid.values()]
        avg_objects_per_cell = sum(objects_per_cell) / len(objects_per_cell) if objects_per_cell else 0
        max_objects_per_cell = max(objects_per_cell) if objects_per_cell else 0
        
        return {
            'total_objects': total_objects,
            'used_cells': used_cells,
            'total_cells': total_cells,
            'usage_percent': (used_cells / total_cells) * 100,
            'avg_objects_per_cell': avg_objects_per_cell,
            'max_objects_per_cell': max_objects_per_cell
        }
