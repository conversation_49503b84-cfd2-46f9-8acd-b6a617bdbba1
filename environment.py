# Клас Environment для управління світом гри
import random
import time
from typing import List, Tuple, Optional
from config import *
from utils import *
from cell import Cell, Food, Poison
from spatial_grid import SpatialGrid

class Environment:
    """Клас для управління ігровим світом."""

    def __init__(self):
        # Списки об'єктів
        self.cells: List[Cell] = []
        self.food: List[Food] = []
        self.poison: List[Poison] = []

        # Статистика
        self.generation = 1
        self.generation_start_time = time.time()
        self.total_cells_created = 0
        self.total_splits = 0
        self.total_absorptions = 0

        # Просторова сітка для оптимізації колізій
        self.spatial_grid = SpatialGrid(WORLD_WIDTH, WORLD_HEIGHT, SPATIAL_GRID_SIZE)
        self.use_spatial_optimization = True

        # Система феромонів (спрощена сітка)
        self.pheromone_map = {}  # Dict[Tuple[int, int], Dict[str, float]]
        self.pheromone_grid_width = WORLD_WIDTH // PHEROMONE_GRID_SIZE
        self.pheromone_grid_height = WORLD_HEIGHT // PHEROMONE_GRID_SIZE
        self.pheromone_update_counter = 0  # Лічильник для рідшого оновлення

        # Ініціалізуємо світ
        self._initialize_world()

    def _initialize_world(self):
        """Ініціалізує світ з початковими об'єктами."""
        # Створюємо їжу
        self.food.clear()
        for _ in range(FOOD_COUNT):
            x, y = random_position_in_world(WORLD_WIDTH, WORLD_HEIGHT)
            self.food.append(Food(x, y))

        # Створюємо отруту
        self.poison.clear()
        for _ in range(POISON_COUNT):
            x, y = random_position_in_world(WORLD_WIDTH, WORLD_HEIGHT)
            self.poison.append(Poison(x, y))

    def add_cell(self, cell: Cell):
        """Додає клітину до середовища."""
        self.cells.append(cell)
        self.total_cells_created += 1

    def create_initial_population(self, genes_list: Optional[List[dict]] = None):
        """Створює початкову популяцію агентів."""
        self.cells.clear()

        for i in range(POPULATION_SIZE):
            # Випадкова позиція
            x, y = random_position_in_world(WORLD_WIDTH, WORLD_HEIGHT, margin=100)

            # Використовуємо передані гени або створюємо нові
            genes = genes_list[i] if genes_list and i < len(genes_list) else None

            cell = Cell(x, y, genes)
            self.add_cell(cell)

    def update(self, dt: float):
        """Оновлює стан всього середовища."""
        # Оновлюємо клітини
        for cell in self.cells[:]:  # Копія списку для безпечного видалення
            if cell.alive:
                cell.update(dt)

                # Отримуємо дані зору
                vision_results = cell.cast_vision_rays(self)

                # Приймаємо рішення про рух
                move_direction = cell.decide_action(vision_results)

                # Рухаємо клітину
                cell.move(move_direction, dt)

                # Перевіряємо поділ
                if cell.can_split():
                    # Розраховуємо заохочення/покарання за розмноження
                    reproduction_reward = cell.calculate_action_reward('reproduction', {'energy': cell.energy})
                    reproduction_penalty = cell.apply_penalty('inefficient_reproduction', {'energy': cell.energy})

                    new_cell = cell.split()
                    if new_cell:
                        # Застосовуємо заохочення за оптимальне розмноження
                        if reproduction_reward > 0:
                            cell.fitness_modifiers += 0.25  # +25% до фітнесу
                            cell.add_reward('optimal_reproduction', reproduction_reward, {'energy': cell.energy})

                        # Застосовуємо покарання за неефективне розмноження
                        if reproduction_penalty > 0:
                            cell.add_penalty('inefficient_reproduction', reproduction_penalty, {'energy': cell.energy})

                        self.add_cell(new_cell)
                        self.total_splits += 1
            else:
                # Видаляємо мертві клітини
                self.cells.remove(cell)

        # Оновлюємо їжу
        for food in self.food:
            food.update(dt)

        # Оновлюємо отруту
        for poison in self.poison:
            poison.update(dt)

        # Оновлюємо феромони рідше (кожен 10-й кадр)
        self.pheromone_update_counter += 1
        if self.pheromone_update_counter >= 10:
            self.update_pheromones(dt * 10)  # Компенсуємо пропущений час
            self.pheromone_update_counter = 0

        # Перевіряємо колізії
        self._check_collisions()

        # Перевіряємо бойові взаємодії
        self._check_combat()

    def _calculate_food_energy(self, cell: Cell, food: Food) -> float:
        """Розраховує енергію, яку отримає клітина від їжі."""
        diet_preference = cell.genes.get('diet_preference', 0.5)

        if food.food_type == 'plant':
            # Рослинна їжа
            efficiency = cell.genes.get('plant_efficiency', 1.0)
            return PLANT_FOOD_ENERGY * efficiency
        else:
            # Білкова їжа
            efficiency = cell.genes.get('protein_efficiency', 1.0)

            if diet_preference > 0.7:  # М'ясоїдний
                return PROTEIN_FOOD_ENERGY_CARNIVORE * efficiency
            elif diet_preference < 0.3:  # Травоїдний
                return PROTEIN_FOOD_ENERGY_HERBIVORE * efficiency
            else:  # Всеїдний
                # Середнє значення
                avg_energy = (PROTEIN_FOOD_ENERGY_CARNIVORE + PROTEIN_FOOD_ENERGY_HERBIVORE) / 2
                return avg_energy * efficiency

    def _check_collisions(self):
        """Перевіряє колізії між об'єктами з оптимізацією через spatial_grid."""
        # Оновлюємо spatial_grid для всіх об'єктів
        if self.use_spatial_optimization:
            self.spatial_grid.clear()

            # Додаємо живих клітин
            for cell in self.cells:
                if cell.alive:
                    self.spatial_grid.add_object(cell, cell.x, cell.y, cell.radius)

            # Додаємо їжу
            for food in self.food:
                if not food.consumed:
                    self.spatial_grid.add_object(food, food.x, food.y, food.radius)

            # Додаємо отруту
            for poison in self.poison:
                if not poison.consumed:
                    self.spatial_grid.add_object(poison, poison.x, poison.y, poison.radius)

        # Колізії клітин з їжею (оптимізовано)
        for cell in self.cells:
            if not cell.alive:
                continue

            if self.use_spatial_optimization:
                # Використовуємо spatial_grid для пошуку поблизу їжі
                nearby_objects = self.spatial_grid.get_nearby_objects(cell.x, cell.y, cell.radius * 2)
                nearby_food = [obj for obj in nearby_objects if hasattr(obj, 'food_type') and not obj.consumed]
            else:
                nearby_food = [food for food in self.food if not food.consumed]

            for food in nearby_food:
                if circles_collide(
                    (cell.x, cell.y), cell.radius,
                    (food.x, food.y), food.radius
                ):
                    # Клітина з'їдає їжу
                    energy_gained = self._calculate_food_energy(cell, food)
                    cell.energy += energy_gained

                    # Система заохочень та покарань за споживання їжі
                    reward = cell.calculate_action_reward('food_consumption', {
                        'food_type': food.food_type,
                        'base_energy': energy_gained
                    })
                    if reward > 0:
                        cell.add_reward('food_consumption', reward, {
                            'food_type': food.food_type,
                            'energy_gained': energy_gained
                        })

                    # Покарання за неправильний тип їжі
                    penalty = cell.apply_penalty('wrong_food', {'food_type': food.food_type})
                    if penalty > 0:
                        cell.add_penalty('wrong_food', penalty, {'food_type': food.food_type})

                    # Оновлюємо статистику споживання
                    if food.food_type == 'plant':
                        cell.plant_food_consumed += 1
                    else:  # protein
                        cell.protein_food_consumed += 1

                    # Скидаємо таймер голоду
                    cell.last_food_consumption_time = 0.0

                    # WORKER залишає феромон їжі при знаходженні
                    if hasattr(cell, 'social_role') and cell.social_role.value == 'worker':
                        self.add_pheromone(cell.x, cell.y, "food", FOOD_PHEROMONE_STRENGTH)

                    food.consume()

        # Колізії клітин з отрутою (оптимізовано)
        for cell in self.cells:
            if not cell.alive:
                continue

            if self.use_spatial_optimization:
                # Використовуємо spatial_grid для пошуку поблизу отрути
                nearby_objects = self.spatial_grid.get_nearby_objects(cell.x, cell.y, cell.radius * 2)
                nearby_poison = [obj for obj in nearby_objects if hasattr(obj, 'energy_loss') and not obj.consumed]
            else:
                nearby_poison = [poison for poison in self.poison if not poison.consumed]

            for poison in nearby_poison:
                if circles_collide(
                    (cell.x, cell.y), cell.radius,
                    (poison.x, poison.y), poison.radius
                ):
                    # Клітина отруюється
                    cell.energy -= poison.energy_loss
                    cell.poison_consumed += 1

                    # Застосовуємо покарання за споживання отрути
                    penalty = cell.apply_penalty('poison', {'energy_loss': poison.energy_loss})
                    cell.add_penalty('poison', penalty, {'energy_loss': poison.energy_loss})

                    # SOLDIER залишає феромон небезпеки при зустрічі з отрутою
                    if hasattr(cell, 'social_role') and cell.social_role.value == 'soldier':
                        self.add_pheromone(cell.x, cell.y, "danger", DANGER_PHEROMONE_STRENGTH)

                    poison.consume()

        # Колізії клітин між собою (оптимізовано)
        alive_cells = [cell for cell in self.cells if cell.alive]

        for i, cell1 in enumerate(alive_cells):
            if self.use_spatial_optimization:
                # Використовуємо spatial_grid для пошуку поблизу клітин
                nearby_objects = self.spatial_grid.get_nearby_objects(cell1.x, cell1.y, cell1.radius * 3)
                nearby_cells = [obj for obj in nearby_objects if hasattr(obj, 'genes') and obj != cell1 and obj.alive]
            else:
                nearby_cells = alive_cells[i+1:]

            for cell2 in nearby_cells:
                if not cell2.alive:  # Додаткова перевірка
                    continue

                # Перевіряємо чи може одна клітина поглинути іншу
                if cell1.can_absorb(cell2):
                    cell1.absorb(cell2)
                    self.total_absorptions += 1
                elif cell2.can_absorb(cell1):
                    cell2.absorb(cell1)
                    self.total_absorptions += 1

    def _check_combat(self):
        """Перевіряє бойові взаємодії між агентами."""
        alive_cells = [cell for cell in self.cells if cell.alive]

        for i, attacker in enumerate(alive_cells):
            # Тільки м'ясоїдні можуть атакувати
            if not attacker.is_carnivore():
                continue

            # Перевіряємо кулдаун атаки
            if attacker.attack_cooldown_timer > 0:
                continue

            # Шукаємо потенційні цілі
            if self.use_spatial_optimization:
                # Використовуємо spatial_grid для пошуку поблизу цілей
                nearby_objects = self.spatial_grid.get_nearby_objects(
                    attacker.x, attacker.y, attacker.attack_range
                )
                potential_targets = [
                    obj for obj in nearby_objects
                    if hasattr(obj, 'genes') and obj != attacker and obj.alive
                ]
            else:
                potential_targets = [cell for cell in alive_cells[i+1:] if cell.alive]

            # Знаходимо найближчу ціль в радіусі атаки
            best_target = None
            best_distance = float('inf')

            for target in potential_targets:
                if attacker.can_attack(target):
                    dist = distance((attacker.x, attacker.y), (target.x, target.y))
                    if dist < best_distance:
                        best_distance = dist
                        best_target = target

            # Атакуємо найближчу ціль
            if best_target:
                initial_target_hp = best_target.current_hp
                if attacker.attack(best_target):
                    # Розраховуємо заохочення за успішну атаку
                    damage_dealt = initial_target_hp - best_target.current_hp
                    reward = attacker.calculate_action_reward('role_performance', {
                        'role': attacker.social_role,
                        'successful_attack': True,
                        'damage_dealt': damage_dealt
                    })
                    if reward > 0:
                        attacker.add_reward('successful_attack', reward, {
                            'damage_dealt': damage_dealt,
                            'target_killed': not best_target.alive
                        })

                    # Покарання за агресивну поведінку травоїдних
                    if not attacker.is_carnivore():
                        penalty = attacker.apply_penalty('aggression', {'herbivore_attack': True})
                        if penalty > 0:
                            attacker.add_penalty('herbivore_aggression', penalty, {
                                'target': best_target.social_role.value if hasattr(best_target, 'social_role') else 'unknown'
                            })

                    # Додаємо феромон небезпеки в місці атаки
                    self.add_pheromone(best_target.x, best_target.y, "danger", DANGER_PHEROMONE_STRENGTH)

                    # Якщо ціль вбито, додаємо статистику
                    if not best_target.alive:
                        self.total_absorptions += 1  # Рахуємо як поглинання для статистики

    def get_alive_cells(self) -> List[Cell]:
        """Повертає список живих клітин."""
        return [cell for cell in self.cells if cell.alive]

    def get_largest_cell(self) -> Optional[Cell]:
        """Повертає найбільшу живу клітину."""
        alive_cells = self.get_alive_cells()
        if not alive_cells:
            return None

        return max(alive_cells, key=lambda cell: cell.radius)

    def get_generation_time(self) -> float:
        """Повертає час поточного покоління в секундах."""
        return time.time() - self.generation_start_time

    def should_restart_generation(self) -> bool:
        """Перевіряє чи потрібно перезапустити покоління."""
        # Перезапуск тільки якщо всі агенти мертві
        if not self.get_alive_cells():
            return True

        return False

    def get_generation_stats(self) -> dict:
        """Повертає статистику поточного покоління."""
        alive_cells = self.get_alive_cells()

        stats = {
            'generation': self.generation,
            'time': self.get_generation_time(),
            'alive_count': len(alive_cells),
            'total_created': self.total_cells_created,
            'total_splits': self.total_splits,
            'total_absorptions': self.total_absorptions,
            'max_energy': 0,
            'avg_energy': 0,
            'max_age': 0,
            'avg_age': 0
        }

        if alive_cells:
            energies = [cell.energy for cell in alive_cells]
            ages = [cell.age for cell in alive_cells]

            stats['max_energy'] = max(energies)
            stats['avg_energy'] = sum(energies) / len(energies)
            stats['max_age'] = max(ages)
            stats['avg_age'] = sum(ages) / len(ages)

        return stats

    def reset_generation(self):
        """Скидає покоління для нового запуску."""
        self.generation += 1
        self.generation_start_time = time.time()
        self.total_cells_created = 0
        self.total_splits = 0
        self.total_absorptions = 0

        # Очищуємо клітини
        self.cells.clear()

        # Перестворюємо їжу та отруту
        self._initialize_world()

    def get_world_bounds(self) -> Tuple[float, float, float, float]:
        """Повертає межі світу (x_min, y_min, x_max, y_max)."""
        return (0, 0, WORLD_WIDTH, WORLD_HEIGHT)

    def get_visible_objects_in_area(self, x: float, y: float, width: float, height: float) -> dict:
        """Повертає об'єкти видимі в заданій області."""
        objects = {
            'cells': [],
            'food': [],
            'poison': []
        }

        # Межі області
        x_min, x_max = x - width/2, x + width/2
        y_min, y_max = y - height/2, y + height/2

        # Клітини
        for cell in self.cells:
            if (cell.alive and
                x_min <= cell.x <= x_max and
                y_min <= cell.y <= y_max):
                objects['cells'].append(cell)

        # Їжа
        for food in self.food:
            if (not food.consumed and
                x_min <= food.x <= x_max and
                y_min <= food.y <= y_max):
                objects['food'].append(food)

        # Отрута
        for poison in self.poison:
            if (not poison.consumed and
                x_min <= poison.x <= x_max and
                y_min <= poison.y <= y_max):
                objects['poison'].append(poison)

        return objects

    def _get_pheromone_grid_coords(self, x: float, y: float) -> Tuple[int, int]:
        """Отримує координати комірки сітки феромонів."""
        grid_x = max(0, min(self.pheromone_grid_width - 1, int(x // PHEROMONE_GRID_SIZE)))
        grid_y = max(0, min(self.pheromone_grid_height - 1, int(y // PHEROMONE_GRID_SIZE)))
        return (grid_x, grid_y)

    def add_pheromone(self, x: float, y: float, pheromone_type: str, strength: float):
        """Додає феромон в заданій позиції."""
        grid_coords = self._get_pheromone_grid_coords(x, y)

        if grid_coords not in self.pheromone_map:
            self.pheromone_map[grid_coords] = {}

        if pheromone_type not in self.pheromone_map[grid_coords]:
            self.pheromone_map[grid_coords][pheromone_type] = 0.0

        self.pheromone_map[grid_coords][pheromone_type] += strength

    def get_pheromone_strength(self, x: float, y: float, pheromone_type: str) -> float:
        """Отримує силу феромону в заданій позиції."""
        grid_coords = self._get_pheromone_grid_coords(x, y)

        if grid_coords in self.pheromone_map:
            return self.pheromone_map[grid_coords].get(pheromone_type, 0.0)

        return 0.0

    def update_pheromones(self, dt: float):
        """Оновлює феромони (розпад)."""
        coords_to_remove = []

        for grid_coords, pheromones in self.pheromone_map.items():
            types_to_remove = []

            for pheromone_type, strength in pheromones.items():
                # Розпад феромону
                new_strength = strength - PHEROMONE_DECAY_RATE * dt

                if new_strength <= 0.01:  # Видаляємо слабкі феромони
                    types_to_remove.append(pheromone_type)
                else:
                    pheromones[pheromone_type] = new_strength

            # Видаляємо слабкі феромони
            for pheromone_type in types_to_remove:
                del pheromones[pheromone_type]

            # Видаляємо порожні комірки
            if not pheromones:
                coords_to_remove.append(grid_coords)

        # Видаляємо порожні комірки
        for grid_coords in coords_to_remove:
            del self.pheromone_map[grid_coords]
