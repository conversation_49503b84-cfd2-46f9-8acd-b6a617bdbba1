# Конфігурація гри Agar.io Clone з ШІ-агентами

# Розміри екрану
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60

# Розміри світу
WORLD_WIDTH = 2000
WORLD_HEIGHT = 2000

# Кольори
BACKGROUND_COLOR = (20, 25, 30)
GRID_COLOR = (40, 45, 50)
WALL_COLOR = (100, 100, 100)

# Кольори їжі
FOOD_COLORS = [
    (100, 255, 100), (100, 200, 255), (255, 200, 100),
    (255, 100, 200), (200, 100, 255), (100, 255, 200),
    (255, 255, 100), (255, 100, 255), (100, 255, 255)
]

# Кольори отрути
POISON_COLOR = (255, 50, 50)

# Кольори агентів
AGENT_COLORS = [
    (80, 150, 255), (255, 150, 80), (150, 255, 80),
    (255, 80, 150), (150, 80, 255), (80, 255, 150),
    (200, 200, 80), (200, 80, 200), (80, 200, 200)
]

# Налаштування агентів
AGENT_START_ENERGY = 60  # Зменшено з 100 для більшого тиску виживання
AGENT_START_RADIUS = 10
AGENT_ENERGY_DECAY = 3.0  # Збільшено з 0.5 для підвищення мотивації пошуку їжі
AGENT_BASE_SPEED = 50.0  # Збільшена швидкість для кращої видимості руху
AGENT_SPLIT_THRESHOLD = 100  # енергія для поділу (знижено з 200)
AGENT_SPLIT_ENERGY_RATIO = 0.5  # частка енергії для кожної нової клітини (50/50 розподіл)
AGENT_MIN_ABSORB_RATIO = 0.8  # мінімальний розмір для поглинання (80% радіуса)

# Система мотивації та цілей
INACTIVITY_ENERGY_PENALTY = 5.0  # штраф за бездіяльність (енергія/сек)
STUCK_DETECTION_TIME = 2.0  # час для виявлення застрягання (секунди)
STUCK_MOVEMENT_THRESHOLD = 5.0  # мінімальний рух за період (пікселі)
HUNGER_PANIC_THRESHOLD = 0.3  # поріг голодної паніки (30% енергії)
HUNGER_PANIC_SPEED_MULTIPLIER = 1.5  # множник швидкості при паніці
HUNGER_PANIC_AGGRESSION_BONUS = 0.5  # бонус агресивності при паніці

# Налаштування їжі
FOOD_COUNT = 450  # Збільшено з 250
FOOD_ENERGY = 15
FOOD_RADIUS = 4
FOOD_RESPAWN_DELAY = 20  # Зменшено з 30 для швидшого респавну

# Типи їжі
PLANT_FOOD_RATIO = 0.6  # 60% рослинної їжі
PROTEIN_FOOD_RATIO = 0.4  # 40% білкової їжі
PLANT_FOOD_COLOR = (0, 255, 0)  # Зелений
PROTEIN_FOOD_COLOR = (255, 0, 0)  # Червоний
PLANT_FOOD_ENERGY = 15  # Базова енергія для всіх
PROTEIN_FOOD_ENERGY_CARNIVORE = 25  # Для м'ясоїдних
PROTEIN_FOOD_ENERGY_HERBIVORE = 5   # Для травоїдних

# Налаштування отрути
POISON_COUNT = 60  # Зменшено з 80
POISON_ENERGY_LOSS = 25
POISON_RADIUS = 5
POISON_COLOR = (128, 0, 255)  # Фіолетовий замість червоного
POISON_RESPAWN_DELAY = 60

# Система зору агентів
VISION_RAYS = 8  # кількість променів
VISION_ANGLES = [i * 45 for i in range(8)]  # кути променів: 0°, 45°, 90°, 135°, 180°, 225°, 270°, 315°
VISION_RANGE_MULTIPLIER = 8  # довжина променя = радіус * множник (збільшено для кращого дослідження)

# Еволюційні налаштування
POPULATION_SIZE = 50
ELITE_RATIO = 0.2  # топ 20% виживають
MUTATION_RATE = 0.3  # ймовірність мутації
MUTATION_STRENGTH = 0.1  # сила мутації (±10%)
# GENERATION_TIME видалено - тільки ручний перезапуск або при смерті всіх агентів
GENES_FILE = "best_genes.json"
STATS_FILE = "generation_stats.csv"

# Розміри UI
MINIMAP_SIZE = 200
MINIMAP_MARGIN = 20
HUD_HEIGHT = 100

# Сітка
GRID_SIZE = 100

# Зум камери
MIN_ZOOM = 0.5
MAX_ZOOM = 3.0
ZOOM_SPEED = 0.1

# Система віртуального часу
TIME_ACCELERATION_FACTOR = 1440  # 1 хвилина реального часу = 1 день віртуального
YOUTH_STAGE_DAYS = 7  # молодість: 0-7 днів
ADULT_STAGE_DAYS = 30  # зрілість: 7-30 днів
ELDER_STAGE_DAYS = 999  # старість: 30+ днів

# Модифікатори етапів життя
YOUTH_SPEED_BONUS = 0.2  # +20% швидкість
ELDER_SPEED_PENALTY = 0.1  # -10% швидкість
ELDER_WISDOM_BONUS = 0.3  # +30% мудрість (кращі рішення)

# Налаштування продуктивності
PERFORMANCE_MODE = "HIGH"  # HIGH (50 агентів), MEDIUM (30), LOW (15)
VISION_UPDATE_FREQUENCY = 3  # оновлювати зір кожен 3-й кадр
SPATIAL_GRID_SIZE = 100  # розмір комірки просторової сітки

# Система еусоціальності та інвентарю
INVENTORY_CAPACITY = 5  # збільшено з 3 до 5 для кращого балансу
PHEROMONE_GRID_SIZE = 50  # розмір комірки сітки феромонів (пікселі)
PHEROMONE_DECAY_RATE = 0.05  # зменшено з 0.1 для довшого життя феромонів
ROLE_INTERACTION_RADIUS = 50.0  # радіус взаємодії між ролями
FOOD_PHEROMONE_STRENGTH = 1.0  # сила феромону їжі
DANGER_PHEROMONE_STRENGTH = 0.8  # сила феромону небезпеки

# Оптимізовані пороги для ролей
QUEEN_ENERGY_THRESHOLD = 150  # енергія для королеви
QUEEN_AGE_THRESHOLD = 14  # вік для королеви (дні)
SOLDIER_AGGRESSION_THRESHOLD = 1.5  # агресія для солдата
SOLDIER_ENERGY_THRESHOLD = 80  # енергія для солдата
DRONE_AGE_THRESHOLD = 7  # максимальний вік для дрона (дні)
DRONE_EXPLORATION_THRESHOLD = 2.5  # дослідження для дрона

# Система бою та здоров'я
HP_PER_RADIUS = 2.0  # HP = radius * 2
HP_REGENERATION_RATE = 1.0  # HP/секунду при енергії > 50%
HP_REGENERATION_ENERGY_THRESHOLD = 0.5  # поріг енергії для регенерації

# Система атаки (для м'ясоїдних)
ATTACK_DAMAGE_BASE = 10.0  # базовий урон
ATTACK_DAMAGE_RADIUS_MULTIPLIER = 0.5  # урон від розміру
ATTACK_RANGE_MULTIPLIER = 1.5  # радіус атаки = radius * 1.5
ATTACK_COOLDOWN = 2.5  # секунди між атаками
ATTACK_ENERGY_COST = 5.0  # енергія за атаку

# Система захисту (для рослиноїдних)
DEFENSE_DAMAGE_REDUCTION = 0.1  # зменшення урону за одиницю захисту
ESCAPE_SPEED_BONUS = 0.5  # +50% швидкості при втечі
ESCAPE_DURATION = 3.0  # секунди бонусу швидкості
CAMOUFLAGE_DETECTION_REDUCTION = 0.3  # -30% радіуса виявлення

# Модифікатори ролей для бою
SOLDIER_ATTACK_MULTIPLIER = 2.0  # +100% урону
WORKER_DEFENSE_MULTIPLIER = 1.5  # +50% захисту
QUEEN_HP_MULTIPLIER = 3.0  # +200% HP
DRONE_ESCAPE_MULTIPLIER = 2.0  # +100% швидкості втечі

# Математичні константи
RADIUS_SCALE = 1.5  # масштабний коефіцієнт для радіуса
