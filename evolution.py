# Система еволюційного навчання для ШІ-агентів
import json
import csv
import random
import copy
import os
from typing import List, Dict, Tuple, Optional
from config import *
from cell import Cell

class EvolutionManager:
    """Менеджер еволюційного навчання."""

    def __init__(self):
        self.generation_history: List[Dict] = []
        self.best_genes_ever: Optional[Dict] = None
        self.best_fitness_ever: float = 0.0

        # Завантажуємо збережені гени якщо є
        self.load_best_genes()

        # Ініціалізуємо файл статистики
        self._initialize_stats_file()

    def _initialize_stats_file(self):
        """Ініціалізує CSV файл для збереження статистики."""
        if not os.path.exists(STATS_FILE):
            with open(STATS_FILE, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow([
                    'Generation', 'Time', 'Alive_Count', 'Total_Created',
                    'Total_Splits', 'Total_Absorptions', 'Max_Energy',
                    'Avg_Energy', 'Max_Age', 'Avg_Age', 'Best_Fitness'
                ])

    def evaluate_population(self, cells: List[Cell]) -> List[Tuple[Cell, float]]:
        """Оцінює фітнес популяції та повертає відсортований список з урахуванням заохочень."""
        if not cells:
            return []

        # Розраховуємо середній час виживання
        total_age = sum(cell.virtual_age for cell in cells if cell.alive)
        avg_age = total_age / len([cell for cell in cells if cell.alive]) if any(cell.alive for cell in cells) else 0

        fitness_scores = []

        for cell in cells:
            # Розраховуємо бонус за виживання понад середнє
            if cell.virtual_age > avg_age:
                survival_bonus_time = cell.virtual_age - avg_age
                # +10% до фітнесу за кожні 10 секунд понад середнє
                survival_bonus = (survival_bonus_time / 10.0) * 0.1
                setattr(cell, 'survival_bonus', survival_bonus)
            else:
                setattr(cell, 'survival_bonus', 0.0)

            fitness = cell.get_fitness()
            fitness_scores.append((cell, fitness))

        # Сортуємо за фітнесом (найкращі спочатку)
        fitness_scores.sort(key=lambda x: x[1], reverse=True)

        return fitness_scores

    def select_elite(self, fitness_scores: List[Tuple[Cell, float]]) -> List[Cell]:
        """Вибирає еліту для наступного покоління."""
        elite_count = max(1, int(len(fitness_scores) * ELITE_RATIO))
        elite_cells = [cell for cell, _ in fitness_scores[:elite_count]]

        return elite_cells

    def crossover(self, parent1: Cell, parent2: Cell) -> Dict[str, float]:
        """Схрещує гени двох батьків."""
        child_genes = {}

        for gene_name in parent1.genes:
            # Випадково вибираємо ген від одного з батьків
            if random.random() < 0.5:
                child_genes[gene_name] = parent1.genes[gene_name]
            else:
                child_genes[gene_name] = parent2.genes[gene_name]

            # Іноді робимо середнє арифметичне
            if random.random() < 0.3:
                child_genes[gene_name] = (parent1.genes[gene_name] + parent2.genes[gene_name]) / 2

        return child_genes

    def mutate_genes(self, genes: Dict[str, float]) -> Dict[str, float]:
        """Мутує гени."""
        mutated_genes = copy.deepcopy(genes)

        for gene_name in mutated_genes:
            if random.random() < MUTATION_RATE:
                # Додаємо випадкову мутацію
                mutation = random.uniform(-MUTATION_STRENGTH, MUTATION_STRENGTH)
                mutated_genes[gene_name] *= (1.0 + mutation)

                # Обмежуємо значення в розумних межах
                mutated_genes[gene_name] = max(0.1, min(mutated_genes[gene_name], 5.0))

        return mutated_genes

    def create_next_generation(self, current_cells: List[Cell]) -> List[Dict[str, float]]:
        """Створює гени для наступного покоління."""
        # Оцінюємо поточну популяцію
        fitness_scores = self.evaluate_population(current_cells)

        if not fitness_scores:
            # Якщо немає живих клітин, створюємо випадкову популяцію
            return [None] * POPULATION_SIZE

        # Оновлюємо найкращі гени
        best_cell, best_fitness = fitness_scores[0]
        if best_fitness > self.best_fitness_ever:
            self.best_fitness_ever = best_fitness
            self.best_genes_ever = copy.deepcopy(best_cell.genes)
            self.save_best_genes()

        # Вибираємо еліту
        elite_cells = self.select_elite(fitness_scores)

        # Створюємо гени для нового покоління
        new_genes = []

        # Додаємо еліту без змін
        for cell in elite_cells:
            new_genes.append(copy.deepcopy(cell.genes))

        # Заповнюємо решту популяції
        while len(new_genes) < POPULATION_SIZE:
            # Вибираємо двох батьків з еліти
            parent1 = random.choice(elite_cells)
            parent2 = random.choice(elite_cells)

            # Схрещуємо
            child_genes = self.crossover(parent1, parent2)

            # Мутуємо
            child_genes = self.mutate_genes(child_genes)

            new_genes.append(child_genes)

        return new_genes

    def save_generation_stats(self, stats: Dict):
        """Зберігає статистику покоління."""
        # Додаємо до історії
        self.generation_history.append(stats.copy())

        # Додаємо найкращий фітнес
        stats['Best_Fitness'] = self.best_fitness_ever

        # Зберігаємо в CSV
        with open(STATS_FILE, 'a', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow([
                stats['generation'], stats['time'], stats['alive_count'],
                stats['total_created'], stats['total_splits'], stats['total_absorptions'],
                stats['max_energy'], stats['avg_energy'], stats['max_age'],
                stats['avg_age'], stats['Best_Fitness']
            ])

    def save_best_genes(self):
        """Зберігає найкращі гени у файл."""
        if self.best_genes_ever:
            data = {
                'genes': self.best_genes_ever,
                'fitness': self.best_fitness_ever,
                'generation': len(self.generation_history)
            }

            with open(GENES_FILE, 'w', encoding='utf-8') as file:
                json.dump(data, file, indent=2, ensure_ascii=False)

    def load_best_genes(self) -> Optional[Dict[str, float]]:
        """Завантажує найкращі гени з файлу."""
        try:
            if os.path.exists(GENES_FILE):
                with open(GENES_FILE, 'r', encoding='utf-8') as file:
                    data = json.load(file)
                    self.best_genes_ever = data.get('genes')
                    self.best_fitness_ever = data.get('fitness', 0.0)
                    return self.best_genes_ever
        except Exception as e:
            print(f"Помилка завантаження генів: {e}")

        return None

    def get_starter_genes(self, count: int) -> List[Optional[Dict[str, float]]]:
        """Повертає стартові гени для нової популяції."""
        genes_list = []

        # Якщо є збережені найкращі гени, використовуємо їх для частини популяції
        if self.best_genes_ever:
            # 30% популяції отримує найкращі гени з мутаціями
            elite_count = min(count // 3, 15)
            for _ in range(elite_count):
                mutated_genes = self.mutate_genes(self.best_genes_ever)
                genes_list.append(mutated_genes)

            # 20% отримує найкращі гени без мутацій
            pure_elite_count = min(count // 5, 10)
            for _ in range(pure_elite_count):
                genes_list.append(copy.deepcopy(self.best_genes_ever))

        # Решта отримує випадкові гени
        while len(genes_list) < count:
            genes_list.append(None)  # None означає випадкові гени

        return genes_list

    def get_evolution_summary(self) -> Dict:
        """Повертає зведення еволюційного процесу."""
        if not self.generation_history:
            return {}

        latest_stats = self.generation_history[-1]

        summary = {
            'total_generations': len(self.generation_history),
            'best_fitness_ever': self.best_fitness_ever,
            'current_generation': latest_stats.get('generation', 0),
            'current_alive': latest_stats.get('alive_count', 0),
            'current_max_energy': latest_stats.get('max_energy', 0),
            'has_saved_genes': self.best_genes_ever is not None
        }

        # Тренди
        if len(self.generation_history) >= 2:
            prev_stats = self.generation_history[-2]
            summary['energy_trend'] = latest_stats.get('max_energy', 0) - prev_stats.get('max_energy', 0)
            summary['survival_trend'] = latest_stats.get('alive_count', 0) - prev_stats.get('alive_count', 0)

        return summary

    def reset_evolution(self):
        """Скидає еволюційний процес."""
        self.generation_history.clear()
        self.best_genes_ever = None
        self.best_fitness_ever = 0.0

        # Видаляємо збережені файли
        if os.path.exists(GENES_FILE):
            os.remove(GENES_FILE)

        if os.path.exists(STATS_FILE):
            os.remove(STATS_FILE)
            self._initialize_stats_file()

    def export_best_genes_for_analysis(self) -> Optional[Dict]:
        """Експортує найкращі гени для аналізу."""
        if not self.best_genes_ever:
            return None

        analysis_data = {
            'genes': self.best_genes_ever,
            'fitness': self.best_fitness_ever,
            'generation_found': len(self.generation_history),
            'gene_analysis': {}
        }

        # Аналізуємо гени
        for gene_name, value in self.best_genes_ever.items():
            analysis_data['gene_analysis'][gene_name] = {
                'value': value,
                'category': self._categorize_gene_value(value),
                'description': self._get_gene_description(gene_name)
            }

        return analysis_data

    def _categorize_gene_value(self, value: float) -> str:
        """Категоризує значення гена."""
        if value < 0.5:
            return 'Низький'
        elif value < 1.5:
            return 'Середній'
        elif value < 2.5:
            return 'Високий'
        else:
            return 'Дуже високий'

    def _get_gene_description(self, gene_name: str) -> str:
        """Повертає опис гена."""
        descriptions = {
            'food_attraction': 'Притягнення до їжі',
            'poison_avoidance': 'Уникнення отрути',
            'cell_avoidance': 'Уникнення інших клітин',
            'wall_avoidance': 'Уникнення стін',
            'close_distance_weight': 'Вага близьких об\'єктів',
            'medium_distance_weight': 'Вага середніх відстаней',
            'far_distance_weight': 'Вага далеких об\'єктів',
            'exploration_tendency': 'Схильність до дослідження',
            'aggression': 'Агресивність',
            'caution': 'Обережність',
            'low_energy_panic': 'Паніка при низькій енергії',
            'high_energy_confidence': 'Впевненість при високій енергії',
            'size_advantage_aggression': 'Агресія при перевазі в розмірі',
            'size_disadvantage_fear': 'Страх при невигідному розмірі',
            'wandering_strength': 'Сила направленого блукання',
            'memory_influence': 'Вплив пам\'яті на рішення',
            'migration_tendency': 'Схильність до міграції',
            'area_boredom': 'Нудьга в одній області',
            'diet_preference': 'Тип харчування (0=травоїдний, 1=м\'ясоїдний)',
            'plant_efficiency': 'Ефективність рослинної їжі',
            'protein_efficiency': 'Ефективність білкової їжі',
            'hunting_aggression': 'Агресивність полювання',
            'herbivore_defense': 'Захист травоїдних'
        }

        return descriptions.get(gene_name, 'Невідомий ген')
