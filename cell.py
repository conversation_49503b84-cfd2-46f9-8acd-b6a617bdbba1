# Клас Cell з ШІ-логікою та променевою системою зору
import math
import random
import copy
from typing import Tuple, List, Dict, Optional
from config import *
from utils import *
from agent_enums import AgentGoal, AgentState, LifeStage, SocialRole

class Cell:
    """ШІ-агент у вигляді клітини з променевою системою зору."""

    def __init__(self, x: float, y: float, genes: Optional[Dict[str, float]] = None):
        # Позиція та фізичні параметри
        self.x = x
        self.y = y
        self.energy = AGENT_START_ENERGY
        self.radius = energy_to_radius(self.energy, RADIUS_SCALE)
        self.velocity_x = 0.0
        self.velocity_y = 0.0

        # Стан агента
        self.alive = True
        self.age = 0
        self.max_energy = AGENT_START_ENERGY
        self.split_cooldown = 0

        # Колір агента (буде оновлений після створення генома)
        self.color_index = random.randint(0, len(AGENT_COLORS) - 1)
        self.base_color = AGENT_COLORS[self.color_index]

        # Оновлюємо колір після створення генома
        if hasattr(self, 'genes'):
            self._update_color_by_diet()

        # Система зору
        self.vision_data = [0.0] * VISION_RAYS  # дані з променів зору
        self.vision_range = self.radius * VISION_RANGE_MULTIPLIER

        # Система пам'яті та дослідження
        self.last_food_time = 0.0  # Час з останнього знаходження їжі
        self.area_time = 0.0       # Час проведений в поточній області
        self.last_area_x = x       # Остання область X
        self.last_area_y = y       # Остання область Y
        self.wandering_direction = random.uniform(0, 2 * math.pi)  # Напрямок блукання
        self.wandering_timer = 0.0  # Таймер для зміни напрямку блукання
        self.migration_timer = 0.0  # Таймер для міграції

        # Статистика споживання
        self.plant_food_consumed = 0
        self.protein_food_consumed = 0
        self.poison_consumed = 0

        # Система заохочень та покарань
        self.total_rewards = 0.0  # Загальна кількість заохочень
        self.total_penalties = 0.0  # Загальна кількість покарань
        self.reward_history = []  # Історія заохочень для аналізу
        self.penalty_history = []  # Історія покарань для аналізу
        self.fitness_modifiers = 0.0  # Модифікатори фітнесу від заохочень/покарань
        self.last_reward_time = 0.0  # Час останнього заохочення
        self.stuck_timer = 0.0  # Таймер для відстеження застрягання
        self.last_position = (x, y)  # Остання позиція для перевірки застрягання

        # Система цілей та мотивації
        self.current_goal = AgentGoal.EXPLORATION
        self.current_state = AgentState.SATISFIED
        self.last_food_consumption_time = 0.0

        # Система виявлення застрягання
        self.movement_history = []  # Список позицій за останні 2 секунди
        self.stuck_timer = 0.0
        self.forced_movement_timer = 0.0
        self.forced_movement_direction = None

        # Віртуальний час та етапи життя
        self.virtual_age = 0.0  # Вік у віртуальних днях
        self.life_stage = LifeStage.YOUTH

        # Оптимізація зору
        self.vision_update_counter = 0
        self.cached_vision_results = []

        # Інвентарна система
        self.inventory_capacity = INVENTORY_CAPACITY
        self.inventory = []  # Список типів їжі: "plant", "protein"

        # Соціальна роль
        self.social_role = SocialRole.WORKER  # За замовчуванням
        self.role_update_timer = 0.0  # Таймер для оновлення ролі

        # Система здоров'я (HP)
        self.max_hp = 0.0  # Буде розраховано після створення генома
        self.current_hp = 0.0  # Поточне здоров'я
        self.hp_regeneration_rate = HP_REGENERATION_RATE

        # Бойова система
        self.attack_damage = 0.0  # Урон за атаку
        self.attack_range = 0.0  # Радіус атаки
        self.attack_cooldown_timer = 0.0  # Таймер кулдауну атаки
        self.defense_value = 0.0  # Значення захисту
        self.escape_timer = 0.0  # Таймер бонусу швидкості втечі
        self.last_attacker = None  # Останній агресор

        # Статистика бою
        self.damage_dealt = 0.0  # Завдано урону
        self.damage_received = 0.0  # Отримано урону
        self.kills = 0  # Кількість вбивств
        self.combat_encounters = 0  # Кількість бойових зустрічей

        # Геном (ваги для прийняття рішень)
        if genes is None:
            self.genes = self._generate_random_genes()
        else:
            self.genes = copy.deepcopy(genes)

        # Оновлюємо колір після створення генома
        self._update_color_by_diet()

        # Ініціалізуємо HP та бойові параметри
        self._initialize_combat_stats()

    def _generate_random_genes(self) -> Dict[str, float]:
        """Генерує випадковий геном для агента."""
        return {
            # Ваги для різних типів об'єктів у зорі
            'food_attraction': random.uniform(1.0, 3.0),  # Збільшено притягнення до їжі
            'poison_avoidance': random.uniform(1.0, 3.0),  # Збільшено уникнення отрути
            'cell_avoidance': random.uniform(0.8, 2.5),  # Збільшено уникнення інших клітин
            'wall_avoidance': random.uniform(1.5, 3.0),  # Збільшено уникнення стін

            # Ваги для відстаней (збільшено вагу далеких об'єктів)
            'close_distance_weight': random.uniform(0.8, 2.0),  # Зменшено локальну фіксацію
            'medium_distance_weight': random.uniform(1.0, 2.5),  # Збільшено
            'far_distance_weight': random.uniform(0.5, 2.0),    # Значно збільшено

            # Поведінкові ваги (збільшено дослідження)
            'exploration_tendency': random.uniform(1.5, 3.5),   # Збільшено мінімум з 1.0 до 1.5
            'aggression': random.uniform(0.2, 1.5),  # Збільшено агресивність
            'caution': random.uniform(0.3, 1.5),  # Зменшено обережність

            # Ваги для енергетичного стану
            'low_energy_panic': random.uniform(1.0, 2.5),  # Збільшено паніку
            'high_energy_confidence': random.uniform(1.2, 2.5),  # Збільшено впевненість

            # Ваги для розміру
            'size_advantage_aggression': random.uniform(0.5, 2.0),  # Збільшено агресію
            'size_disadvantage_fear': random.uniform(1.0, 3.0),  # Збільшено страх

            # Нові гени для покращеного дослідження
            'wandering_strength': random.uniform(1.0, 2.5),     # Збільшено мінімум з 0.5 до 1.0
            'memory_influence': random.uniform(0.3, 1.5),       # Вплив пам'яті
            'migration_tendency': random.uniform(0.2, 1.0),     # Схильність до міграції
            'area_boredom': random.uniform(0.1, 0.8),           # "Нудьга" в одній області

            # Гени системи харчування
            'diet_preference': random.uniform(0.0, 1.0),        # 0.0=травоїдний, 1.0=м'ясоїдний
            'plant_efficiency': random.uniform(0.5, 2.0),       # Ефективність рослинної їжі
            'protein_efficiency': random.uniform(0.5, 2.0),     # Ефективність білкової їжі
            'hunting_aggression': random.uniform(0.1, 2.0),     # Агресивність полювання
            'herbivore_defense': random.uniform(0.5, 2.0),      # Захист травоїдних

            # Гени бойової системи
            'combat_preference': random.uniform(0.0, 1.0),      # Схильність до бою (0.0=пацифіст, 1.0=агресивний)
            'defensive_instinct': random.uniform(0.0, 1.0),     # Інстинкт захисту (0.0=безрозсудний, 1.0=обережний)
            'pack_hunting': random.uniform(0.0, 1.0)            # Схильність до групового полювання
        }

    def _update_color_by_diet(self):
        """Оновлює колір агента залежно від типу харчування."""
        diet_pref = self.genes.get('diet_preference', 0.5)
        base_color = AGENT_COLORS[self.color_index]

        if diet_pref < 0.3:  # Травоїдний
            # Додаємо зеленуватий відтінок
            self.base_color = (
                min(255, int(base_color[0] * 0.8)),
                min(255, int(base_color[1] * 1.2)),
                min(255, int(base_color[2] * 0.9))
            )
        elif diet_pref > 0.7:  # М'ясоїдний
            # Додаємо червонуватий відтінок
            self.base_color = (
                min(255, int(base_color[0] * 1.2)),
                min(255, int(base_color[1] * 0.8)),
                min(255, int(base_color[2] * 0.8))
            )
        else:  # Всеїдний
            self.base_color = base_color

    def _initialize_combat_stats(self):
        """Ініціалізує HP та бойові параметри на основі генома та розміру."""
        # Розраховуємо максимальне HP
        base_hp = self.radius * HP_PER_RADIUS

        # Модифікатори ролей
        if self.social_role == SocialRole.QUEEN:
            base_hp *= QUEEN_HP_MULTIPLIER

        self.max_hp = base_hp
        self.current_hp = self.max_hp

        # Розраховуємо урон атаки (для м'ясоїдних)
        diet_pref = self.genes.get('diet_preference', 0.5)
        if diet_pref > 0.5:  # М'ясоїдні
            base_damage = ATTACK_DAMAGE_BASE + (self.radius * ATTACK_DAMAGE_RADIUS_MULTIPLIER)
            combat_bonus = self.genes.get('combat_preference', 0.5)
            aggression_bonus = self.genes.get('aggression', 1.0)

            self.attack_damage = base_damage * combat_bonus * aggression_bonus

            # Модифікатор ролі (застосовуємо після базового розрахунку)
            if self.social_role == SocialRole.SOLDIER:
                self.attack_damage *= SOLDIER_ATTACK_MULTIPLIER
        else:
            self.attack_damage = 0.0  # Травоїдні не атакують

        # Розраховуємо радіус атаки
        self.attack_range = self.radius * ATTACK_RANGE_MULTIPLIER

        # Розраховуємо захист (для травоїдних)
        if diet_pref < 0.5:  # Травоїдні
            base_defense = self.genes.get('herbivore_defense', 1.0)
            defensive_instinct = self.genes.get('defensive_instinct', 0.5)

            self.defense_value = base_defense * defensive_instinct

            # Модифікатор ролі
            if self.social_role == SocialRole.WORKER:
                self.defense_value *= WORKER_DEFENSE_MULTIPLIER
        else:
            self.defense_value = 0.0  # М'ясоїдні не мають захисту

    def get_diet_type(self) -> str:
        """Повертає тип харчування агента."""
        diet_pref = self.genes.get('diet_preference', 0.5)
        if diet_pref < 0.3:
            return 'травоїдний'
        elif diet_pref > 0.7:
            return 'м\'ясоїдний'
        else:
            return 'всеїдний'

    def get_diet_percentages(self) -> Dict[str, float]:
        """Повертає відсоткове співвідношення типів харчування."""
        total_food = self.plant_food_consumed + self.protein_food_consumed
        if total_food == 0:
            return {'рослинна': 0.0, 'білкова': 0.0}

        return {
            'рослинна': (self.plant_food_consumed / total_food) * 100,
            'білкова': (self.protein_food_consumed / total_food) * 100
        }

    def determine_current_goal(self) -> AgentGoal:
        """Визначає поточну ціль агента на основі енергії."""
        energy_ratio = self.energy / AGENT_START_ENERGY

        if energy_ratio < 0.5:  # Енергія < 50%
            return AgentGoal.SURVIVAL
        elif energy_ratio > 1.5:  # Енергія > 150%
            return AgentGoal.REPRODUCTION
        else:  # Середня енергія
            return AgentGoal.EXPLORATION

    def determine_current_state(self) -> AgentState:
        """Визначає поточний стан агента на основі енергії."""
        # Використовуємо абсолютні значення енергії замість відношення
        if self.energy < 18:  # 30% від 60
            return AgentState.HUNGRY
        elif self.energy > 42:  # 70% від 60
            return AgentState.FULL
        else:  # Енергія 18-42
            return AgentState.SATISFIED

    def get_life_stage(self) -> LifeStage:
        """Визначає етап життя агента на основі віртуального віку."""
        if self.virtual_age < YOUTH_STAGE_DAYS:
            return LifeStage.YOUTH
        elif self.virtual_age < ADULT_STAGE_DAYS:
            return LifeStage.ADULT
        else:
            return LifeStage.ELDER

    def detect_stuck_behavior(self) -> bool:
        """Виявляє чи агент застряг на місці."""
        if len(self.movement_history) < 2:
            return False

        # Обчислюємо загальну відстань руху за останні 2 секунди
        total_distance = 0.0
        for i in range(1, len(self.movement_history)):
            prev_pos = self.movement_history[i-1]
            curr_pos = self.movement_history[i]
            total_distance += math.sqrt((curr_pos[0] - prev_pos[0])**2 + (curr_pos[1] - prev_pos[1])**2)

        return total_distance < STUCK_MOVEMENT_THRESHOLD

    def get_virtual_age_display(self) -> str:
        """Повертає відформатований віртуальний вік для відображення."""
        stage_name = {
            LifeStage.YOUTH: "молодість",
            LifeStage.ADULT: "зрілість",
            LifeStage.ELDER: "старість"
        }[self.life_stage]

        return f"{self.virtual_age:.1f} дні ({stage_name})"

    def can_carry_food(self) -> bool:
        """Перевіряє чи є місце в інвентарі.

        Returns:
            bool: True якщо є місце, False якщо інвентар повний
        """
        return len(self.inventory) < self.inventory_capacity

    def add_to_inventory(self, food_type: str) -> bool:
        """Додає їжу до інвентарю.

        Args:
            food_type: Тип їжі ('plant' або 'protein')

        Returns:
            bool: True якщо їжу додано, False якщо немає місця
        """
        if self.can_carry_food():
            self.inventory.append(food_type)
            return True
        return False

    def transfer_food_to(self, other_cell: 'Cell', food_type: str) -> bool:
        """Передає їжу іншому агенту.

        Args:
            other_cell: Агент-отримувач
            food_type: Тип їжі для передачі

        Returns:
            bool: True якщо передача успішна, False інакше
        """
        if food_type in self.inventory and other_cell.can_carry_food():
            self.inventory.remove(food_type)
            other_cell.inventory.append(food_type)
            return True
        return False

    def get_inventory_display(self) -> str:
        """Повертає відформатований інвентар для відображення."""
        if not self.inventory:
            return "Порожній"

        plant_count = self.inventory.count("plant")
        protein_count = self.inventory.count("protein")

        items = []
        if plant_count > 0:
            items.append(f"🌱{plant_count}")
        if protein_count > 0:
            items.append(f"🥩{protein_count}")

        return " ".join(items)

    def determine_social_role(self) -> SocialRole:
        """Визначає соціальну роль агента на основі характеристик.

        Returns:
            SocialRole: Роль агента (QUEEN/SOLDIER/DRONE/WORKER)
        """
        # QUEEN: використовуємо константи з config.py
        if (self.energy > QUEEN_ENERGY_THRESHOLD and
            self.virtual_age > QUEEN_AGE_THRESHOLD and
            self.radius > 15):  # Приблизно топ 5% за розміром
            return SocialRole.QUEEN

        # SOLDIER: використовуємо константи з config.py
        if (self.genes.get('aggression', 0) > SOLDIER_AGGRESSION_THRESHOLD and
            self.energy > SOLDIER_ENERGY_THRESHOLD and
            self.virtual_age > DRONE_AGE_THRESHOLD):
            return SocialRole.SOLDIER

        # DRONE: використовуємо константи з config.py
        if (self.virtual_age < DRONE_AGE_THRESHOLD and
            self.genes.get('exploration_tendency', 0) > DRONE_EXPLORATION_THRESHOLD):
            return SocialRole.DRONE

        # WORKER: всі інші (за замовчуванням)
        return SocialRole.WORKER

    def can_attack(self, target: 'Cell') -> bool:
        """Перевіряє чи може агент атакувати ціль."""
        if not (self.alive and target.alive):
            return False

        # Тільки м'ясоїдні можуть атакувати
        diet_pref = self.genes.get('diet_preference', 0.5)
        if diet_pref <= 0.5:
            return False

        # Перевіряємо кулдаун
        if self.attack_cooldown_timer > 0:
            return False

        # Перевіряємо відстань
        dist = distance((self.x, self.y), (target.x, target.y))
        return dist <= self.attack_range

    def attack(self, target: 'Cell') -> bool:
        """Атакує ціль."""
        if not self.can_attack(target):
            return False

        # Перевіряємо енергію для атаки
        if self.energy < ATTACK_ENERGY_COST:
            return False

        # Витрачаємо енергію
        self.energy -= ATTACK_ENERGY_COST

        # Розраховуємо урон
        damage = self.attack_damage

        # Застосовуємо урон до цілі
        actual_damage = target.take_damage(damage, self)

        # Оновлюємо статистику
        self.damage_dealt += actual_damage
        self.combat_encounters += 1

        # Встановлюємо кулдаун
        self.attack_cooldown_timer = ATTACK_COOLDOWN

        return True

    def take_damage(self, damage: float, attacker: 'Cell') -> float:
        """Отримує урон від атакуючого."""
        if not self.alive:
            return 0.0

        # Застосовуємо захист (для травоїдних)
        diet_pref = self.genes.get('diet_preference', 0.5)
        if diet_pref < 0.5:  # Травоїдні
            damage_reduction = self.defense_value * DEFENSE_DAMAGE_REDUCTION
            damage *= (1.0 - min(damage_reduction, 0.8))  # Максимум 80% зменшення

            # Активуємо бонус швидкості втечі
            if self.escape_timer <= 0:
                self.escape_timer = ESCAPE_DURATION

        # Застосовуємо урон
        actual_damage = min(damage, self.current_hp)
        self.current_hp -= actual_damage

        # Оновлюємо статистику
        self.damage_received += actual_damage
        self.last_attacker = attacker

        # Перевіряємо смерть
        if self.current_hp <= 0:
            self.alive = False
            if attacker:
                attacker.kills += 1

        return actual_damage

    def get_hp_ratio(self) -> float:
        """Повертає відношення поточного HP до максимального."""
        if self.max_hp <= 0:
            return 1.0
        return self.current_hp / self.max_hp

    def is_carnivore(self) -> bool:
        """Перевіряє чи є агент м'ясоїдним."""
        return self.genes.get('diet_preference', 0.5) > 0.5

    def is_herbivore(self) -> bool:
        """Перевіряє чи є агент травоїдним."""
        return self.genes.get('diet_preference', 0.5) < 0.5

    def get_escape_speed_multiplier(self) -> float:
        """Повертає множник швидкості втечі."""
        if self.escape_timer > 0:
            base_bonus = ESCAPE_SPEED_BONUS
            if self.social_role == SocialRole.DRONE:
                base_bonus *= DRONE_ESCAPE_MULTIPLIER
            return 1.0 + base_bonus
        return 1.0

    def calculate_action_reward(self, action_type: str, context: dict = None) -> float:
        """Розраховує заохочення за дію.

        Args:
            action_type: Тип дії ('food_consumption', 'role_performance', 'cooperation', 'reproduction')
            context: Додатковий контекст для розрахунку

        Returns:
            float: Розмір заохочення (позитивне число)
        """
        if context is None:
            context = {}

        reward = 0.0

        if action_type == 'food_consumption':
            food_type = context.get('food_type', 'plant')
            diet_pref = self.genes.get('diet_preference', 0.5)

            # Заохочення за правильний тип їжі
            if food_type == 'plant' and diet_pref < 0.5:  # Травоїдний їсть рослини
                reward = 5.0
            elif food_type == 'protein' and diet_pref > 0.5:  # М'ясоїдний їсть протеїн
                reward = 5.0
            elif food_type == 'plant' and self.social_role == SocialRole.WORKER:
                # WORKER отримує +20% енергії від рослинної їжі
                reward = context.get('base_energy', 15) * 0.2

        elif action_type == 'role_performance':
            role = context.get('role', self.social_role)

            if role == SocialRole.SOLDIER and context.get('successful_attack', False):
                # SOLDIER +15% за успішні атаки
                damage_dealt = context.get('damage_dealt', 0)
                reward = damage_dealt * 0.15

        elif action_type == 'cooperation':
            # Кооперативна поведінка (обмін ресурсами)
            if context.get('resource_transfer', False):
                reward = 5.0

        elif action_type == 'reproduction':
            energy = context.get('energy', self.energy)
            # Оптимальне розмноження при 100-120 енергії
            if 100 <= energy <= 120:
                reward = 25.0  # +25% до фітнесу (буде застосовано пізніше)

        return reward

    def apply_penalty(self, penalty_type: str, context: dict = None) -> float:
        """Застосовує покарання за дію.

        Args:
            penalty_type: Тип покарання ('wrong_food', 'poison', 'stuck', 'aggression', 'inefficient_reproduction')
            context: Додатковий контекст для розрахунку

        Returns:
            float: Розмір покарання (позитивне число, буде віднято)
        """
        if context is None:
            context = {}

        penalty = 0.0

        if penalty_type == 'wrong_food':
            food_type = context.get('food_type', 'plant')
            diet_pref = self.genes.get('diet_preference', 0.5)

            # Покарання за неправильний тип їжі
            if food_type == 'protein' and diet_pref < 0.5:  # Травоїдний їсть протеїн
                penalty = 3.0

        elif penalty_type == 'poison':
            # Споживання отрути
            penalty = 25.0  # Базове покарання
            self.fitness_modifiers -= 0.05  # -5% до фітнесу

        elif penalty_type == 'stuck':
            # Тривале застрягання
            penalty = 2.0  # За секунду застрягання

        elif penalty_type == 'aggression':
            # Агресивна поведінка без причини
            if context.get('herbivore_attack', False):
                penalty = 10.0

        elif penalty_type == 'inefficient_reproduction':
            energy = context.get('energy', self.energy)
            # Неефективне розмноження
            if energy < 100 or energy > 150:
                self.fitness_modifiers -= 0.15  # -15% до фітнесу

        return penalty

    def add_reward(self, reward_type: str, amount: float, context: dict = None):
        """Додає заохочення до агента."""
        if amount > 0:
            self.total_rewards += amount
            self.energy += amount
            self.reward_history.append({
                'type': reward_type,
                'amount': amount,
                'time': self.age,
                'context': context or {}
            })
            self.last_reward_time = self.age

    def add_penalty(self, penalty_type: str, amount: float, context: dict = None):
        """Додає покарання до агента."""
        if amount > 0:
            self.total_penalties += amount
            self.energy -= amount
            self.penalty_history.append({
                'type': penalty_type,
                'amount': amount,
                'time': self.age,
                'context': context or {}
            })

    def get_reward_penalty_ratio(self) -> float:
        """Повертає співвідношення заохочень до покарань."""
        if self.total_penalties == 0:
            return float('inf') if self.total_rewards > 0 else 1.0
        return self.total_rewards / self.total_penalties

    def check_stuck_behavior(self, dt: float):
        """Перевіряє та застосовує покарання за застрягання."""
        # Перевіряємо чи агент рухається
        current_pos = (self.x, self.y)
        distance_moved = distance(current_pos, self.last_position)

        if distance_moved < 1.0:  # Рух менше 1 пікселя
            self.stuck_timer += dt
            if self.stuck_timer > 15.0:  # 15 секунд застрягання
                penalty = self.apply_penalty('stuck', {'duration': dt})
                self.add_penalty('stuck', penalty, {'position': current_pos})
        else:
            self.stuck_timer = 0.0

        self.last_position = current_pos

    def update(self, dt: float):
        """Оновлює стан клітини."""
        if not self.alive:
            return

        # Збільшуємо вік
        self.age += dt

        # Оновлюємо віртуальний вік
        self.virtual_age = (self.age * TIME_ACCELERATION_FACTOR) / 60.0  # В днях

        # Оновлюємо етап життя
        self.life_stage = self.get_life_stage()

        # Оновлюємо цілі та стани
        self.current_goal = self.determine_current_goal()
        self.current_state = self.determine_current_state()

        # Оновлюємо соціальну роль кожні 5 секунд
        self.role_update_timer += dt
        if self.role_update_timer > 5.0:
            old_role = self.social_role
            self.social_role = self.determine_social_role()
            # Якщо роль змінилася, оновлюємо бойові параметри
            if old_role != self.social_role:
                self._initialize_combat_stats()
            self.role_update_timer = 0.0

        # Оновлюємо таймери
        self.last_food_consumption_time += dt

        # Оновлюємо бойові таймери
        if self.attack_cooldown_timer > 0:
            self.attack_cooldown_timer -= dt

        if self.escape_timer > 0:
            self.escape_timer -= dt

        # Оновлюємо HP (регенерація)
        energy_ratio = self.energy / AGENT_START_ENERGY
        if energy_ratio > HP_REGENERATION_ENERGY_THRESHOLD and self.current_hp < self.max_hp:
            self.current_hp = min(self.max_hp, self.current_hp + self.hp_regeneration_rate * dt)

        # Прогресивна втрата енергії (збільшується з часом без їжі)
        hunger_time = self.last_food_consumption_time
        energy_decay = AGENT_ENERGY_DECAY * (1 + hunger_time / 10.0)
        self.energy -= energy_decay * dt

        # Оновлюємо радіус на основі енергії
        old_radius = self.radius
        self.radius = energy_to_radius(self.energy, RADIUS_SCALE)
        self.vision_range = self.radius * VISION_RANGE_MULTIPLIER

        # Оновлюємо максимальне HP при зміні розміру
        if abs(old_radius - self.radius) > 0.1:  # Якщо радіус змінився значно
            old_max_hp = self.max_hp
            base_hp = self.radius * HP_PER_RADIUS
            if self.social_role == SocialRole.QUEEN:
                base_hp *= QUEEN_HP_MULTIPLIER
            self.max_hp = base_hp

            # Пропорційно збільшуємо поточне HP
            if old_max_hp > 0:
                hp_ratio = self.current_hp / old_max_hp
                self.current_hp = self.max_hp * hp_ratio

        # Зменшуємо кулдаун поділу
        if self.split_cooldown > 0:
            self.split_cooldown -= dt

        # Оновлюємо систему пам'яті та дослідження
        self.last_food_time += dt
        self.area_time += dt
        self.wandering_timer += dt
        self.migration_timer += dt

        # Перевіряємо чи агент залишається в тій же області
        area_size = 200  # Розмір області для відстеження
        current_area_x = int(self.x // area_size)
        current_area_y = int(self.y // area_size)

        if (current_area_x != int(self.last_area_x // area_size) or
            current_area_y != int(self.last_area_y // area_size)):
            # Агент перемістився в нову область
            self.area_time = 0.0
            self.last_area_x = self.x
            self.last_area_y = self.y

        # Змінюємо напрямок блукання періодично
        if self.wandering_timer > random.uniform(3.0, 8.0):  # Кожні 3-8 секунд
            self.wandering_direction = random.uniform(0, 2 * math.pi)
            self.wandering_timer = 0.0

        # Оновлюємо історію руху для виявлення застрягання (фіксований розмір)
        self.movement_history.append((self.x, self.y))
        # Фіксований розмір для оптимізації пам'яті
        MAX_HISTORY_SIZE = 120  # 2 секунди при 60 FPS
        if len(self.movement_history) > MAX_HISTORY_SIZE:
            self.movement_history.pop(0)

        # Перевіряємо застрягання
        if self.detect_stuck_behavior():
            self.stuck_timer += dt
            if self.stuck_timer > STUCK_DETECTION_TIME:
                # Форсуємо випадковий рух
                self.forced_movement_timer = random.uniform(1.0, 2.0)
                self.forced_movement_direction = random.uniform(0, 2 * math.pi)
                self.stuck_timer = 0.0
        else:
            self.stuck_timer = 0.0

        # Зменшуємо таймер форсованого руху
        if self.forced_movement_timer > 0:
            self.forced_movement_timer -= dt

        # Штраф за бездіяльність
        current_speed = math.sqrt(self.velocity_x**2 + self.velocity_y**2)
        if current_speed < 2.0:  # Швидкість менше 2 пікселів/сек
            self.energy -= INACTIVITY_ENERGY_PENALTY * dt

        # Перевіряємо застрягання та застосовуємо покарання
        self.check_stuck_behavior(dt)

        # Перевіряємо смерть (енергія або HP)
        if self.energy <= 0 or self.current_hp <= 0:
            self.alive = False
            return

        # Оновлюємо максимальну енергію
        if self.energy > self.max_energy:
            self.max_energy = self.energy

    def cast_vision_rays(self, environment) -> List[Tuple[str, float]]:
        """Випускає промені зору та повертає інформацію про об'єкти."""
        # Оптимізація: оновлюємо зір не кожен кадр
        self.vision_update_counter += 1
        if self.vision_update_counter < VISION_UPDATE_FREQUENCY and self.cached_vision_results:
            return self.cached_vision_results

        self.vision_update_counter = 0
        vision_results = []

        for angle_deg in VISION_ANGLES:
            angle_rad = math.radians(angle_deg)

            # Напрямок променя
            ray_dx = math.cos(angle_rad)
            ray_dy = math.sin(angle_rad)

            # Кінцева точка променя
            ray_end_x = self.x + ray_dx * self.vision_range
            ray_end_y = self.y + ray_dy * self.vision_range

            # Знаходимо найближчий об'єкт на промені
            closest_object = None
            closest_distance = self.vision_range

            # Перевіряємо стіни
            if ray_end_x < 0 or ray_end_x > WORLD_WIDTH or ray_end_y < 0 or ray_end_y > WORLD_HEIGHT:
                # Обчислюємо відстань до стіни
                wall_distance = self._distance_to_wall(self.x, self.y, ray_dx, ray_dy)
                if wall_distance < closest_distance:
                    closest_distance = wall_distance
                    closest_object = 'wall'

            # Перевіряємо їжу
            for food in environment.food:
                if not food.consumed:  # Додаткова перевірка
                    intersection = line_circle_intersection(
                        (self.x, self.y), (ray_end_x, ray_end_y),
                        (food.x, food.y), food.radius
                    )
                    if intersection is not None:  # Явна перевірка на None
                        dist = distance((self.x, self.y), intersection)
                        if dist < closest_distance:
                            closest_distance = dist
                            closest_object = 'food'

            # Перевіряємо отруту
            for poison in environment.poison:
                if not poison.consumed:  # Додаткова перевірка
                    intersection = line_circle_intersection(
                        (self.x, self.y), (ray_end_x, ray_end_y),
                        (poison.x, poison.y), poison.radius
                    )
                    if intersection is not None:  # Явна перевірка на None
                        dist = distance((self.x, self.y), intersection)
                        if dist < closest_distance:
                            closest_distance = dist
                            closest_object = 'poison'

            # Перевіряємо інші клітини
            for cell in environment.cells:
                if cell != self and cell.alive:
                    intersection = line_circle_intersection(
                        (self.x, self.y), (ray_end_x, ray_end_y),
                        (cell.x, cell.y), cell.radius
                    )
                    if intersection is not None:  # Явна перевірка на None
                        dist = distance((self.x, self.y), intersection)
                        if dist < closest_distance:
                            closest_distance = dist
                            closest_object = 'cell'

            # Зберігаємо результат
            if closest_object:
                vision_results.append((closest_object, closest_distance))
            else:
                vision_results.append(('empty', self.vision_range))

        # Кешуємо результати для оптимізації
        self.cached_vision_results = vision_results
        return vision_results

    def _distance_to_wall(self, x: float, y: float, dx: float, dy: float) -> float:
        """Обчислює відстань до найближчої стіни в напрямку променя."""
        distances = []

        # Відстань до лівої стіни
        if dx < 0:
            distances.append(x / abs(dx))

        # Відстань до правої стіни
        if dx > 0:
            distances.append((WORLD_WIDTH - x) / dx)

        # Відстань до верхньої стіни
        if dy < 0:
            distances.append(y / abs(dy))

        # Відстань до нижньої стіни
        if dy > 0:
            distances.append((WORLD_HEIGHT - y) / dy)

        return min(distances) if distances else self.vision_range

    def decide_action(self, vision_results: List[Tuple[str, float]]) -> Tuple[float, float]:
        """Приймає рішення про рух на основі даних зору та генома."""
        move_x = 0.0
        move_y = 0.0

        # Перевіряємо чи бачимо їжу
        food_seen = any(obj_type == 'food' for obj_type, _ in vision_results)
        if food_seen:
            self.last_food_time = 0.0  # Скидаємо таймер голоду

        # Форсований рух при застряганні
        if self.forced_movement_timer > 0 and self.forced_movement_direction is not None:
            # Подвоєна швидкість при форсованому русі
            force_x = math.cos(self.forced_movement_direction) * 2.0
            force_y = math.sin(self.forced_movement_direction) * 2.0
            return normalize_vector((force_x, force_y))

        # Аналізуємо кожен промінь зору
        for i, (obj_type, distance) in enumerate(vision_results):
            if obj_type == 'empty':
                continue

            # Отримуємо напрямок променя
            angle_rad = math.radians(VISION_ANGLES[i])
            ray_dx = math.cos(angle_rad)
            ray_dy = math.sin(angle_rad)

            # Нормалізуємо відстань (0 = дуже близько, 1 = дуже далеко)
            normalized_distance = min(distance / self.vision_range, 1.0)

            # Обчислюємо вагу на основі відстані (тепер віддаємо перевагу далеким об'єктам)
            if normalized_distance < 0.3:
                distance_weight = self.genes['close_distance_weight']
            elif normalized_distance < 0.7:
                distance_weight = self.genes['medium_distance_weight']
            else:
                distance_weight = self.genes['far_distance_weight']

            # Обчислюємо силу впливу
            influence_strength = (1.0 - normalized_distance) * distance_weight

            # Застосовуємо поведінку залежно від типу об'єкта
            if obj_type == 'food':
                # Рухаємося до їжі з підвищеною силою для далеких об'єктів
                force = influence_strength * self.genes['food_attraction']
                # Бонус для далекої їжі
                if normalized_distance > 0.5:
                    force *= 1.5  # Збільшуємо притягнення до далекої їжі
                move_x += ray_dx * force
                move_y += ray_dy * force

            elif obj_type == 'poison':
                # Уникаємо отрути
                force = influence_strength * self.genes['poison_avoidance']
                move_x -= ray_dx * force
                move_y -= ray_dy * force

            elif obj_type == 'wall':
                # Уникаємо стін
                force = influence_strength * self.genes['wall_avoidance']
                move_x -= ray_dx * force
                move_y -= ray_dy * force

            elif obj_type == 'cell':
                # Бойова поведінка для м'ясоїдних
                if self.is_carnivore() and self.genes.get('combat_preference', 0.5) > 0.5:
                    # М'ясоїдні з високою схильністю до бою рухаються до інших клітин
                    force = influence_strength * self.genes.get('combat_preference', 0.5)
                    move_x += ray_dx * force
                    move_y += ray_dy * force
                else:
                    # Інші агенти уникають інших клітин
                    force = influence_strength * self.genes['cell_avoidance']
                    move_x -= ray_dx * force
                    move_y -= ray_dy * force

        # Додаємо направлене блукання
        wandering_force = self.genes['wandering_strength']
        wander_dx = math.cos(self.wandering_direction) * wandering_force
        wander_dy = math.sin(self.wandering_direction) * wandering_force
        move_x += wander_dx
        move_y += wander_dy

        # Додаємо міграційний інстинкт якщо довго не знаходимо їжу
        if self.last_food_time > 10.0:  # 10 секунд без їжі
            migration_strength = self.genes['migration_tendency'] * (self.last_food_time / 10.0)
            # Рухаємося до центру світу або в випадковому напрямку
            center_x = WORLD_WIDTH / 2
            center_y = WORLD_HEIGHT / 2
            to_center_x = center_x - self.x
            to_center_y = center_y - self.y
            center_distance = math.sqrt(to_center_x**2 + to_center_y**2)

            if center_distance > 100:  # Якщо далеко від центру
                move_x += (to_center_x / center_distance) * migration_strength
                move_y += (to_center_y / center_distance) * migration_strength
            else:  # Якщо близько до центру, рухаємося випадково
                random_angle = random.uniform(0, 2 * math.pi)
                move_x += math.cos(random_angle) * migration_strength
                move_y += math.sin(random_angle) * migration_strength

        # Додаємо "нудьгу" в одній області
        if self.area_time > 15.0:  # 15 секунд в одній області
            boredom_strength = self.genes['area_boredom'] * (self.area_time / 15.0)
            # Рухаємося в напрямку, протилежному до центру поточної області
            area_center_x = (int(self.x // 200) + 0.5) * 200
            area_center_y = (int(self.y // 200) + 0.5) * 200
            away_x = self.x - area_center_x
            away_y = self.y - area_center_y
            away_distance = math.sqrt(away_x**2 + away_y**2)

            if away_distance > 1:
                move_x += (away_x / away_distance) * boredom_strength
                move_y += (away_y / away_distance) * boredom_strength

        # Базове дослідження (завжди активне)
        exploration_force = self.genes['exploration_tendency']
        move_x += random.uniform(-exploration_force, exploration_force)
        move_y += random.uniform(-exploration_force, exploration_force)

        # Якщо все ще немає сильних стимулів, форсуємо рух
        total_force = math.sqrt(move_x * move_x + move_y * move_y)
        if total_force < 0.5:  # Слабкий рух
            # Додаємо сильний випадковий рух
            random_angle = random.uniform(0, 2 * math.pi)
            random_strength = random.uniform(1.0, 2.0)
            move_x += math.cos(random_angle) * random_strength
            move_y += math.sin(random_angle) * random_strength

        # Модифікуємо поведінку залежно від цілі
        goal_modifier = 1.0
        if self.current_goal == AgentGoal.SURVIVAL:
            goal_modifier = 1.3  # Більша активність при виживанні
        elif self.current_goal == AgentGoal.REPRODUCTION:
            goal_modifier = 0.8  # Менша активність при готовності до поділу
        elif self.current_goal == AgentGoal.EXPLORATION:
            goal_modifier = 1.1  # Трохи більша активність при дослідженні

        move_x *= goal_modifier
        move_y *= goal_modifier

        # Голодна паніка
        energy_ratio = self.energy / AGENT_START_ENERGY
        if energy_ratio < HUNGER_PANIC_THRESHOLD:  # Голодна паніка
            panic_multiplier = HUNGER_PANIC_SPEED_MULTIPLIER
            aggression_bonus = HUNGER_PANIC_AGGRESSION_BONUS

            # Збільшуємо швидкість та агресивність
            move_x *= panic_multiplier
            move_y *= panic_multiplier

            # Додаємо додаткове притягнення до їжі
            for i, (obj_type, distance) in enumerate(vision_results):
                if obj_type == 'food':
                    angle_rad = math.radians(VISION_ANGLES[i])
                    ray_dx = math.cos(angle_rad)
                    ray_dy = math.sin(angle_rad)

                    panic_food_force = aggression_bonus * (1.0 - min(distance / self.vision_range, 1.0))
                    move_x += ray_dx * panic_food_force
                    move_y += ray_dy * panic_food_force

        # Модифікатори етапів життя
        life_stage_modifier = 1.0
        if self.life_stage == LifeStage.YOUTH:
            life_stage_modifier = 1.0 + YOUTH_SPEED_BONUS
        elif self.life_stage == LifeStage.ELDER:
            life_stage_modifier = 1.0 - ELDER_SPEED_PENALTY
            # Старші агенти мудріші - кращі рішення
            if self.life_stage == LifeStage.ELDER:
                wisdom_bonus = ELDER_WISDOM_BONUS
                # Покращуємо притягнення до їжі та уникнення небезпек
                for i, (obj_type, distance) in enumerate(vision_results):
                    if obj_type in ['food', 'poison']:
                        angle_rad = math.radians(VISION_ANGLES[i])
                        ray_dx = math.cos(angle_rad)
                        ray_dy = math.sin(angle_rad)

                        wisdom_force = wisdom_bonus * (1.0 - min(distance / self.vision_range, 1.0))
                        if obj_type == 'food':
                            move_x += ray_dx * wisdom_force
                            move_y += ray_dy * wisdom_force
                        elif obj_type == 'poison':
                            move_x -= ray_dx * wisdom_force
                            move_y -= ray_dy * wisdom_force

        move_x *= life_stage_modifier
        move_y *= life_stage_modifier

        # Поведінкові модифікатори соціальних ролей
        role_modifier = 1.0
        if self.social_role == SocialRole.WORKER:
            # WORKER: food_attraction × 1.4, групується
            for i, (obj_type, distance) in enumerate(vision_results):
                if obj_type == 'food':
                    angle_rad = math.radians(VISION_ANGLES[i])
                    ray_dx = math.cos(angle_rad)
                    ray_dy = math.sin(angle_rad)

                    worker_food_bonus = 0.4 * (1.0 - min(distance / self.vision_range, 1.0))
                    move_x += ray_dx * worker_food_bonus
                    move_y += ray_dy * worker_food_bonus

        elif self.social_role == SocialRole.SOLDIER:
            # SOLDIER: aggression × 1.6, швидкість × 1.1, патрулює межі
            role_modifier = 1.1

            # Тяжіння до меж світу для патрулювання
            center_x = WORLD_WIDTH / 2
            center_y = WORLD_HEIGHT / 2
            to_edge_x = self.x - center_x
            to_edge_y = self.y - center_y
            edge_distance = math.sqrt(to_edge_x**2 + to_edge_y**2)

            if edge_distance > 50:  # Якщо далеко від центру
                patrol_strength = 0.3
                move_x += (to_edge_x / edge_distance) * patrol_strength
                move_y += (to_edge_y / edge_distance) * patrol_strength

        elif self.social_role == SocialRole.QUEEN:
            # QUEEN: швидкість × 0.4, тяжіння до центру
            role_modifier = 0.4

            # Тяжіння до центру світу
            center_x = WORLD_WIDTH / 2
            center_y = WORLD_HEIGHT / 2
            to_center_x = center_x - self.x
            to_center_y = center_y - self.y
            center_distance = math.sqrt(to_center_x**2 + to_center_y**2)

            if center_distance > 100:  # Якщо далеко від центру
                center_strength = 0.5
                move_x += (to_center_x / center_distance) * center_strength
                move_y += (to_center_y / center_distance) * center_strength

        elif self.social_role == SocialRole.DRONE:
            # DRONE: exploration_tendency × 1.3, уникає скупчень
            role_modifier = 1.3

            # Додаткове дослідження
            exploration_bonus = 0.5
            move_x += random.uniform(-exploration_bonus, exploration_bonus)
            move_y += random.uniform(-exploration_bonus, exploration_bonus)

        move_x *= role_modifier
        move_y *= role_modifier

        # Нормалізуємо вектор руху
        move_vector = normalize_vector((move_x, move_y))

        return move_vector

    def move(self, direction: Tuple[float, float], dt: float):
        """Рухає клітину в заданому напрямку."""
        if not self.alive:
            return

        # Обчислюємо швидкість на основі розміру
        speed = calculate_speed(self.radius, AGENT_BASE_SPEED)

        # Застосовуємо бонус швидкості втечі
        escape_multiplier = self.get_escape_speed_multiplier()
        speed *= escape_multiplier

        # Оновлюємо швидкість
        self.velocity_x = direction[0] * speed
        self.velocity_y = direction[1] * speed

        # Оновлюємо позицію
        new_x = self.x + self.velocity_x * dt
        new_y = self.y + self.velocity_y * dt

        # Обмежуємо позицію в межах світу
        old_x, old_y = self.x, self.y
        self.x, self.y = clamp_position((new_x, new_y), self.radius, WORLD_WIDTH, WORLD_HEIGHT)

        # Додаткова перевірка для налагодження
        if abs(direction[0]) > 0.01 or abs(direction[1]) > 0.01:
            # Агент намагається рухатися
            distance_moved = math.sqrt((self.x - old_x)**2 + (self.y - old_y)**2)
            if distance_moved < 0.01:
                # Агент застряг - додаємо невеликий випадковий зсув
                self.x += random.uniform(-1, 1)
                self.y += random.uniform(-1, 1)
                # Переконуємося що все ще в межах світу
                self.x, self.y = clamp_position((self.x, self.y), self.radius, WORLD_WIDTH, WORLD_HEIGHT)

    def can_split(self) -> bool:
        """Перевіряє чи може клітина поділитися."""
        return (self.alive and
                self.energy >= AGENT_SPLIT_THRESHOLD and
                self.split_cooldown <= 0)

    def split(self) -> Optional['Cell']:
        """Ділить клітину на дві."""
        if not self.can_split():
            return None

        # Зменшуємо енергію батьківської клітини
        new_energy = self.energy * AGENT_SPLIT_ENERGY_RATIO
        self.energy = new_energy

        # Створюємо нову клітину
        offset_x = random.uniform(-self.radius, self.radius)
        offset_y = random.uniform(-self.radius, self.radius)

        new_cell = Cell(
            self.x + offset_x,
            self.y + offset_y,
            self.genes  # Передаємо геном
        )
        new_cell.energy = new_energy

        # Встановлюємо кулдаун
        self.split_cooldown = 5.0  # 5 секунд
        new_cell.split_cooldown = 5.0

        return new_cell

    def can_absorb(self, other: 'Cell') -> bool:
        """Перевіряє чи може ця клітина поглинути іншу."""
        if not (self.alive and other.alive):
            return False

        # Перевіряємо розмір
        min_radius_to_absorb = other.radius * AGENT_MIN_ABSORB_RATIO
        if self.radius < min_radius_to_absorb:
            return False

        # Перевіряємо відстань
        dist = distance((self.x, self.y), (other.x, other.y))
        return dist < (self.radius + other.radius) * 0.8

    def absorb(self, other: 'Cell'):
        """Поглинає іншу клітину."""
        if self.can_absorb(other):
            # Отримуємо енергію
            energy_gain = other.energy * 0.8  # 80% енергії жертви
            self.energy += energy_gain

            # Вбиваємо жертву
            other.alive = False

    def get_fitness(self) -> float:
        """Обчислює фітнес агента для еволюції з урахуванням заохочень та покарань."""
        # Базовий фітнес на основі енергії та віку
        energy_score = self.max_energy / AGENT_START_ENERGY
        virtual_survival_score = self.virtual_age / 30.0  # нормалізуємо до 30 днів

        # Бонус за досягнення різних етапів життя
        life_stage_bonus = 0.0
        if self.life_stage == LifeStage.ADULT:
            life_stage_bonus = 0.1  # Бонус за досягнення зрілості
        elif self.life_stage == LifeStage.ELDER:
            life_stage_bonus = 0.2  # Більший бонус за досягнення старості

        # Базовий фітнес
        base_fitness = energy_score * 0.6 + virtual_survival_score * 0.3 + life_stage_bonus

        # Додаємо модифікатори від системи заохочень/покарань
        reward_penalty_modifier = 0.0

        # Бонус за співвідношення заохочень до покарань
        if self.total_penalties > 0:
            ratio = self.total_rewards / self.total_penalties
            if ratio > 2.0:  # Більше заохочень ніж покарань
                reward_penalty_modifier += 0.1
            elif ratio < 0.5:  # Більше покарань ніж заохочень
                reward_penalty_modifier -= 0.1
        elif self.total_rewards > 0:
            reward_penalty_modifier += 0.15  # Бонус за заохочення без покарань

        # Бонус за виживання понад середній час (буде розраховано в evolution.py)
        survival_bonus = getattr(self, 'survival_bonus', 0.0)

        # Застосовуємо всі модифікатори
        final_fitness = base_fitness * (1.0 + self.fitness_modifiers) + reward_penalty_modifier + survival_bonus

        return max(0.0, final_fitness)  # Фітнес не може бути негативним

    def mutate(self, mutation_rate: float = MUTATION_RATE,
               mutation_strength: float = MUTATION_STRENGTH):
        """Мутує геном агента."""
        for gene_name in self.genes:
            if random.random() < mutation_rate:
                # Додаємо випадкову мутацію
                mutation = random.uniform(-mutation_strength, mutation_strength)
                self.genes[gene_name] *= (1.0 + mutation)

                # Обмежуємо значення в розумних межах
                self.genes[gene_name] = clamp(self.genes[gene_name], 0.1, 5.0)


class Food:
    """Клас їжі для агентів з підтримкою типів."""

    def __init__(self, x: float, y: float, food_type: str = None):
        self.x = x
        self.y = y
        self.radius = FOOD_RADIUS
        self.energy = FOOD_ENERGY

        # Визначаємо тип їжі
        if food_type is None:
            self.food_type = 'plant' if random.random() < PLANT_FOOD_RATIO else 'protein'
        else:
            self.food_type = food_type

        # Встановлюємо колір залежно від типу
        if self.food_type == 'plant':
            self.color = PLANT_FOOD_COLOR
        else:  # protein
            self.color = PROTEIN_FOOD_COLOR

        self.consumed = False
        self.respawn_timer = 0

    def update(self, dt: float):
        """Оновлює стан їжі."""
        if self.consumed and self.respawn_timer > 0:
            self.respawn_timer -= dt
            if self.respawn_timer <= 0:
                self.respawn()

    def consume(self):
        """Споживає їжу."""
        if not self.consumed:
            self.consumed = True
            self.respawn_timer = FOOD_RESPAWN_DELAY

    def respawn(self):
        """Відновлює їжу в новому місці."""
        self.x, self.y = random_position_in_world(WORLD_WIDTH, WORLD_HEIGHT)

        # Перевизначаємо тип їжі при респавні
        self.food_type = 'plant' if random.random() < PLANT_FOOD_RATIO else 'protein'

        # Оновлюємо колір
        if self.food_type == 'plant':
            self.color = PLANT_FOOD_COLOR
        else:  # protein
            self.color = PROTEIN_FOOD_COLOR

        self.consumed = False


class Poison:
    """Клас отрути для агентів."""

    def __init__(self, x: float, y: float):
        self.x = x
        self.y = y
        self.radius = POISON_RADIUS
        self.energy_loss = POISON_ENERGY_LOSS
        self.color = POISON_COLOR
        self.consumed = False
        self.respawn_timer = 0

    def update(self, dt: float):
        """Оновлює стан отрути."""
        if self.consumed and self.respawn_timer > 0:
            self.respawn_timer -= dt
            if self.respawn_timer <= 0:
                self.respawn()

    def consume(self):
        """Споживає отруту."""
        if not self.consumed:
            self.consumed = True
            self.respawn_timer = POISON_RESPAWN_DELAY

    def respawn(self):
        """Відновлює отруту в новому місці."""
        self.x, self.y = random_position_in_world(WORLD_WIDTH, WORLD_HEIGHT)
        self.consumed = False
