# Pygame візуалізація у стилі .io ігор
import pygame
import math
from typing import Tuple, Optional, Dict
from config import *
from utils import *
from environment import Environment
from agent_enums import AgentGoal, AgentState, SocialRole, GOAL_COLORS, ROLE_COLORS
from cell import Cell

class Camera:
    """Камера для слідкування за об'єктами або вільного керування."""

    def __init__(self):
        self.x = WORLD_WIDTH / 2
        self.y = WORLD_HEIGHT / 2
        self.zoom = 1.0
        self.target_x = self.x
        self.target_y = self.y
        self.target_zoom = self.zoom
        self.smooth_factor = 0.1

        # Режими камери
        self.free_camera_mode = True  # Починаємо з вільної камери
        self.camera_speed = 300.0  # пікселів за секунду

        # Стан клавіш для вільної камери
        self.keys_pressed = {
            'up': False,
            'down': False,
            'left': False,
            'right': False
        }

    def set_free_camera_mode(self, enabled: bool):
        """Увімкнути/вимкнути режим вільної камери."""
        self.free_camera_mode = enabled
        if enabled:
            # При увімкненні вільної камери, встановлюємо поточну позицію як цільову
            self.target_x = self.x
            self.target_y = self.y

    def update_key_state(self, key: str, pressed: bool):
        """Оновлює стан клавіш для вільної камери."""
        if key in self.keys_pressed:
            self.keys_pressed[key] = pressed

    def follow_cell(self, cell: Optional[Cell]):
        """Слідкує за клітиною (тільки якщо не в режимі вільної камери)."""
        if not self.free_camera_mode and cell and cell.alive:
            self.target_x = cell.x
            self.target_y = cell.y

            # Автоматичний зум на основі розміру клітини
            base_zoom = 1.0
            size_factor = cell.radius / AGENT_START_RADIUS
            self.target_zoom = clamp(base_zoom / (size_factor * 0.5), MIN_ZOOM, MAX_ZOOM)

    def update(self, dt: float):
        """Оновлює позицію камери з плавною анімацією."""
        if self.free_camera_mode:
            # Вільне керування камерою
            move_speed = self.camera_speed / self.zoom  # Швидкість залежить від зуму

            if self.keys_pressed['up']:
                self.target_y -= move_speed * dt
            if self.keys_pressed['down']:
                self.target_y += move_speed * dt
            if self.keys_pressed['left']:
                self.target_x -= move_speed * dt
            if self.keys_pressed['right']:
                self.target_x += move_speed * dt

            # Обмежуємо камеру в межах світу з відступом
            margin = 200 / self.zoom
            self.target_x = clamp(self.target_x, margin, WORLD_WIDTH - margin)
            self.target_y = clamp(self.target_y, margin, WORLD_HEIGHT - margin)

        # Плавне переміщення до цільової позиції
        self.x = lerp(self.x, self.target_x, self.smooth_factor)
        self.y = lerp(self.y, self.target_y, self.smooth_factor)
        self.zoom = lerp(self.zoom, self.target_zoom, self.smooth_factor * 0.5)

    def world_to_screen(self, world_x: float, world_y: float) -> Tuple[int, int]:
        """Конвертує світові координати в екранні."""
        screen_x = (world_x - self.x) * self.zoom + SCREEN_WIDTH / 2
        screen_y = (world_y - self.y) * self.zoom + SCREEN_HEIGHT / 2
        return (int(screen_x), int(screen_y))

    def screen_to_world(self, screen_x: int, screen_y: int) -> Tuple[float, float]:
        """Конвертує екранні координати в світові."""
        world_x = (screen_x - SCREEN_WIDTH / 2) / self.zoom + self.x
        world_y = (screen_y - SCREEN_HEIGHT / 2) / self.zoom + self.y
        return (world_x, world_y)

    def get_visible_area(self) -> Tuple[float, float, float, float]:
        """Повертає видиму область світу."""
        half_width = SCREEN_WIDTH / (2 * self.zoom)
        half_height = SCREEN_HEIGHT / (2 * self.zoom)

        return (
            self.x - half_width,
            self.y - half_height,
            self.x + half_width,
            self.y + half_height
        )


class Visualizer:
    """Головний клас візуалізації."""

    def __init__(self):
        pygame.init()

        # Створюємо вікно
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Agar.io Clone з ШІ-агентами")

        # Камера
        self.camera = Camera()

        # Налаштування
        self.show_vision = False
        self.show_debug = True
        self.paused = False

        # Система вибору агентів
        self.selected_agent = None
        self.follow_selected = False
        self.show_restart_confirmation = False

        # Шрифти
        self.font_small = pygame.font.Font(None, 24)
        self.font_medium = pygame.font.Font(None, 32)
        self.font_large = pygame.font.Font(None, 48)

        # Кольори для UI
        self.ui_bg_color = (0, 0, 0, 180)
        self.ui_text_color = (255, 255, 255)
        self.ui_accent_color = (100, 200, 255)

    def handle_events(self, environment) -> Dict[str, bool]:
        """Обробляє події pygame."""
        events = {
            'quit': False,
            'restart': False,
            'toggle_vision': False,
            'toggle_pause': False,
            'toggle_camera': False,
            'confirm_restart': False,
            'cancel_restart': False
        }

        # Обробляємо події клавіатури
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                events['quit'] = True

            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    if self.show_restart_confirmation:
                        self.show_restart_confirmation = False
                    else:
                        events['quit'] = True
                elif event.key == pygame.K_r:
                    if not self.show_restart_confirmation:
                        self.show_restart_confirmation = True
                elif event.key == pygame.K_y:
                    if self.show_restart_confirmation:
                        events['confirm_restart'] = True
                        self.show_restart_confirmation = False
                elif event.key == pygame.K_n:
                    if self.show_restart_confirmation:
                        events['cancel_restart'] = True
                        self.show_restart_confirmation = False
                elif event.key == pygame.K_v:
                    events['toggle_vision'] = True
                    self.show_vision = not self.show_vision
                elif event.key == pygame.K_p:
                    events['toggle_pause'] = True
                    self.paused = not self.paused
                elif event.key == pygame.K_c:
                    events['toggle_camera'] = True
                    self.camera.set_free_camera_mode(not self.camera.free_camera_mode)
                elif event.key == pygame.K_f:
                    if self.selected_agent and self.selected_agent.alive:
                        self.follow_selected = not self.follow_selected
                        if self.follow_selected:
                            self.camera.set_free_camera_mode(False)

                # Керування вільною камерою
                elif event.key in [pygame.K_w, pygame.K_UP]:
                    self.camera.update_key_state('up', True)
                elif event.key in [pygame.K_s, pygame.K_DOWN]:
                    self.camera.update_key_state('down', True)
                elif event.key in [pygame.K_a, pygame.K_LEFT]:
                    self.camera.update_key_state('left', True)
                elif event.key in [pygame.K_d, pygame.K_RIGHT]:
                    self.camera.update_key_state('right', True)

            elif event.type == pygame.KEYUP:
                # Відпускання клавіш керування камерою
                if event.key in [pygame.K_w, pygame.K_UP]:
                    self.camera.update_key_state('up', False)
                elif event.key in [pygame.K_s, pygame.K_DOWN]:
                    self.camera.update_key_state('down', False)
                elif event.key in [pygame.K_a, pygame.K_LEFT]:
                    self.camera.update_key_state('left', False)
                elif event.key in [pygame.K_d, pygame.K_RIGHT]:
                    self.camera.update_key_state('right', False)

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Лівий клік миші
                    self._handle_mouse_click(event.pos, environment)

            elif event.type == pygame.MOUSEWHEEL:
                # Зум камери
                zoom_factor = 1.1 if event.y > 0 else 0.9
                self.camera.target_zoom = clamp(
                    self.camera.target_zoom * zoom_factor,
                    MIN_ZOOM, MAX_ZOOM
                )

        return events

    def _handle_mouse_click(self, mouse_pos: Tuple[int, int], environment):
        """Обробляє клік миші для вибору агентів."""
        # Конвертуємо екранні координати в світові
        world_x, world_y = self.camera.screen_to_world(mouse_pos[0], mouse_pos[1])

        # Шукаємо найближчого агента
        closest_agent = None
        closest_distance = float('inf')

        for cell in environment.get_alive_cells():
            distance = math.sqrt((cell.x - world_x)**2 + (cell.y - world_y)**2)
            # Перевіряємо чи клік попав в агента
            if distance <= cell.radius and distance < closest_distance:
                closest_distance = distance
                closest_agent = cell

        # Оновлюємо вибраного агента
        if closest_agent:
            self.selected_agent = closest_agent
            self.follow_selected = False
        else:
            # Клік по порожньому місцю - скасовуємо вибір
            self.selected_agent = None
            self.follow_selected = False

    def update_camera(self, environment: Environment, dt: float):
        """Оновлює камеру."""
        # Перевіряємо чи вибраний агент ще живий
        if self.selected_agent and not self.selected_agent.alive:
            self.selected_agent = None
            self.follow_selected = False

        # Слідкуємо за вибраним агентом або найбільшою клітиною
        if self.follow_selected and self.selected_agent:
            self.camera.follow_cell(self.selected_agent)
        else:
            largest_cell = environment.get_largest_cell()
            self.camera.follow_cell(largest_cell)

        self.camera.update(dt)

    def render(self, environment: Environment):
        """Рендерить всю сцену."""
        # Очищуємо екран
        self.screen.fill(BACKGROUND_COLOR)

        # Рендерим сітку
        self._render_grid()

        # Рендерим об'єкти світу
        self._render_world_objects(environment)

        # Рендерим UI
        self._render_ui(environment)

        # Рендерим міні-карту
        self._render_minimap(environment)

        # Рендерим інформацію про вибраного агента
        if self.selected_agent:
            self._render_agent_info(self.selected_agent)

        # Показуємо паузу якщо потрібно
        if self.paused:
            self._render_pause_overlay()

        # Показуємо підтвердження перезапуску
        if self.show_restart_confirmation:
            self._render_restart_confirmation()

        pygame.display.flip()

    def _render_grid(self):
        """Рендерить сітку світу."""
        x_min, y_min, x_max, y_max = self.camera.get_visible_area()

        # Вертикальні лінії
        start_x = int(x_min // GRID_SIZE) * GRID_SIZE
        for x in range(start_x, int(x_max) + GRID_SIZE, GRID_SIZE):
            screen_x1, screen_y1 = self.camera.world_to_screen(x, y_min)
            screen_x2, screen_y2 = self.camera.world_to_screen(x, y_max)

            if 0 <= screen_x1 <= SCREEN_WIDTH:
                pygame.draw.line(self.screen, GRID_COLOR,
                               (screen_x1, max(0, screen_y1)),
                               (screen_x2, min(SCREEN_HEIGHT, screen_y2)))

        # Горизонтальні лінії
        start_y = int(y_min // GRID_SIZE) * GRID_SIZE
        for y in range(start_y, int(y_max) + GRID_SIZE, GRID_SIZE):
            screen_x1, screen_y1 = self.camera.world_to_screen(x_min, y)
            screen_x2, screen_y2 = self.camera.world_to_screen(x_max, y)

            if 0 <= screen_y1 <= SCREEN_HEIGHT:
                pygame.draw.line(self.screen, GRID_COLOR,
                               (max(0, screen_x1), screen_y1),
                               (min(SCREEN_WIDTH, screen_x2), screen_y2))

    def _render_world_objects(self, environment: Environment):
        """Рендерить об'єкти світу."""
        # Отримуємо видиму область
        x_min, y_min, x_max, y_max = self.camera.get_visible_area()
        visible_objects = environment.get_visible_objects_in_area(
            self.camera.x, self.camera.y,
            (x_max - x_min), (y_max - y_min)
        )

        # Рендерим їжу
        for food in visible_objects['food']:
            self._render_food(food)

        # Рендерим отруту
        for poison in visible_objects['poison']:
            self._render_poison(poison)

        # Рендерим клітини (сортуємо за розміром)
        cells = sorted(visible_objects['cells'], key=lambda c: c.radius)
        for cell in cells:
            self._render_cell(cell)

            # Виділяємо вибраного агента
            if cell == self.selected_agent:
                self._render_selected_outline(cell)

            # Відображаємо ціль агента
            self._render_agent_goal(cell)

            # Відображаємо соціальну роль
            self._render_social_role(cell)

            # Відображаємо HP бар
            self._render_hp_bar(cell)

            # Відображаємо бойові ефекти
            self._render_combat_effects(cell)

            # Рендерим промені зору якщо потрібно
            if self.show_vision:
                self._render_vision_rays(cell, environment)

    def _render_food(self, food):
        """Рендерить їжу."""
        screen_x, screen_y = self.camera.world_to_screen(food.x, food.y)
        radius = max(1, int(food.radius * self.camera.zoom))

        if (0 <= screen_x <= SCREEN_WIDTH and
            0 <= screen_y <= SCREEN_HEIGHT and
            radius > 0):

            # Основне коло
            pygame.draw.circle(self.screen, food.color, (screen_x, screen_y), radius)

            # Блик
            highlight_radius = max(1, radius // 3)
            highlight_color = tuple(min(255, c + 50) for c in food.color)
            pygame.draw.circle(self.screen, highlight_color,
                             (screen_x - radius//3, screen_y - radius//3),
                             highlight_radius)

    def _render_poison(self, poison):
        """Рендерить отруту."""
        screen_x, screen_y = self.camera.world_to_screen(poison.x, poison.y)
        radius = max(1, int(poison.radius * self.camera.zoom))

        if (0 <= screen_x <= SCREEN_WIDTH and
            0 <= screen_y <= SCREEN_HEIGHT and
            radius > 0):

            # Основне коло
            pygame.draw.circle(self.screen, poison.color, (screen_x, screen_y), radius)

            # Темний контур
            pygame.draw.circle(self.screen, (150, 0, 0), (screen_x, screen_y), radius, 2)

    def _render_cell(self, cell: Cell):
        """Рендерить клітину з градієнтним ефектом."""
        screen_x, screen_y = self.camera.world_to_screen(cell.x, cell.y)
        radius = max(2, int(cell.radius * self.camera.zoom))

        if (0 <= screen_x <= SCREEN_WIDTH and
            0 <= screen_y <= SCREEN_HEIGHT and
            radius > 1):

            # Створюємо градієнт від центру до країв
            base_color = cell.base_color

            # Рендерим кілька кіл для градієнтного ефекту
            for i in range(radius, 0, -max(1, radius // 8)):
                t = 1.0 - (i / radius)

                # Колір від темного краю до світлого центру
                edge_color = tuple(int(c * 0.6) for c in base_color)
                center_color = tuple(min(255, int(c * 1.3)) for c in base_color)

                current_color = create_gradient_color(center_color, edge_color, t)
                pygame.draw.circle(self.screen, current_color, (screen_x, screen_y), i)

            # Контур
            outline_color = tuple(int(c * 0.4) for c in base_color)
            pygame.draw.circle(self.screen, outline_color, (screen_x, screen_y), radius, 2)

            # Блик
            if radius > 5:
                highlight_radius = max(2, radius // 4)
                highlight_color = tuple(min(255, c + 80) for c in base_color)
                pygame.draw.circle(self.screen, highlight_color,
                                 (screen_x - radius//3, screen_y - radius//3),
                                 highlight_radius)

    def _render_vision_rays(self, cell: Cell, environment: Environment):
        """Рендерить промені зору клітини."""
        if cell.radius * self.camera.zoom < 10:  # Не показуємо для дуже маленьких клітин
            return

        screen_x, screen_y = self.camera.world_to_screen(cell.x, cell.y)
        vision_results = cell.cast_vision_rays(environment)

        for i, (obj_type, distance) in enumerate(vision_results):
            angle_rad = math.radians(VISION_ANGLES[i])

            # Кінцева точка променя
            end_x = cell.x + math.cos(angle_rad) * distance
            end_y = cell.y + math.sin(angle_rad) * distance
            screen_end_x, screen_end_y = self.camera.world_to_screen(end_x, end_y)

            # Колір променя залежно від об'єкта
            if obj_type == 'food':
                ray_color = (0, 255, 0, 100)  # Зелений
            elif obj_type == 'poison':
                ray_color = (255, 0, 0, 100)  # Червоний
            elif obj_type == 'cell':
                ray_color = (255, 255, 0, 100)  # Жовтий
            elif obj_type == 'wall':
                ray_color = (128, 128, 128, 100)  # Сірий
            else:
                ray_color = (64, 64, 64, 50)  # Темно-сірий

            # Рендерим промінь
            pygame.draw.line(self.screen, ray_color[:3],
                           (screen_x, screen_y), (screen_end_x, screen_end_y), 1)

            # Точка на кінці променя
            if obj_type != 'empty':
                pygame.draw.circle(self.screen, ray_color[:3],
                                 (screen_end_x, screen_end_y), 3)

    def _render_selected_outline(self, cell):
        """Рендерить білий контур навколо вибраного агента."""
        screen_x, screen_y = self.camera.world_to_screen(cell.x, cell.y)
        radius = max(2, int(cell.radius * self.camera.zoom))

        if (0 <= screen_x <= SCREEN_WIDTH and
            0 <= screen_y <= SCREEN_HEIGHT and
            radius > 1):
            # Білий контур товщиною 3 пікселі
            pygame.draw.circle(self.screen, (255, 255, 255), (screen_x, screen_y), radius + 3, 3)

    def _render_agent_goal(self, cell):
        """Рендерить візуальне відображення цілі агента."""
        screen_x, screen_y = self.camera.world_to_screen(cell.x, cell.y)
        radius = max(2, int(cell.radius * self.camera.zoom))

        if (0 <= screen_x <= SCREEN_WIDTH and
            0 <= screen_y <= SCREEN_HEIGHT and
            radius > 1):

            # Отримуємо колір цілі
            goal_color = GOAL_COLORS.get(cell.current_goal, (255, 255, 255))

            # Рендерим тонкий контур навколо агента
            pygame.draw.circle(self.screen, goal_color, (screen_x, screen_y), radius + 1, 1)

    def _render_social_role(self, cell):
        """Рендерить візуальне відображення соціальної ролі агента."""
        screen_x, screen_y = self.camera.world_to_screen(cell.x, cell.y)
        radius = max(2, int(cell.radius * self.camera.zoom))

        if (0 <= screen_x <= SCREEN_WIDTH and
            0 <= screen_y <= SCREEN_HEIGHT and
            radius > 1):

            if hasattr(cell, 'social_role'):
                if cell.social_role == SocialRole.WORKER:
                    # WORKER: маленький квадрат (інвентар) якщо є їжа
                    if hasattr(cell, 'inventory') and cell.inventory:
                        square_size = max(2, radius // 3)
                        square_x = screen_x + radius - square_size
                        square_y = screen_y - radius
                        pygame.draw.rect(self.screen, (100, 255, 100),
                                       (square_x, square_y, square_size, square_size))

                elif cell.social_role == SocialRole.SOLDIER:
                    # SOLDIER: товстий червоний контур
                    role_color = ROLE_COLORS.get(cell.social_role, (255, 255, 255))
                    pygame.draw.circle(self.screen, role_color, (screen_x, screen_y), radius + 2, 3)

                elif cell.social_role == SocialRole.QUEEN:
                    # QUEEN: золотий контур + маленький трикутник зверху (корона)
                    role_color = ROLE_COLORS.get(cell.social_role, (255, 255, 255))
                    pygame.draw.circle(self.screen, role_color, (screen_x, screen_y), radius + 2, 2)

                    # Корона (трикутник)
                    crown_size = max(3, radius // 2)
                    crown_points = [
                        (screen_x, screen_y - radius - crown_size),
                        (screen_x - crown_size//2, screen_y - radius),
                        (screen_x + crown_size//2, screen_y - radius)
                    ]
                    pygame.draw.polygon(self.screen, role_color, crown_points)

                elif cell.social_role == SocialRole.DRONE:
                    # DRONE: пунктирний синій контур
                    role_color = ROLE_COLORS.get(cell.social_role, (255, 255, 255))
                    # Імітуємо пунктир малими дугами
                    for angle in range(0, 360, 30):
                        start_angle = math.radians(angle)
                        end_angle = math.radians(angle + 15)
                        arc_rect = pygame.Rect(screen_x - radius - 1, screen_y - radius - 1,
                                             (radius + 1) * 2, (radius + 1) * 2)
                        pygame.draw.arc(self.screen, role_color, arc_rect, start_angle, end_angle, 2)

    def _render_hp_bar(self, cell):
        """Рендерить HP бар над клітиною."""
        # Показуємо HP бар тільки якщо HP не повне або клітина вибрана
        hp_ratio = cell.get_hp_ratio()
        if hp_ratio >= 0.99 and cell != self.selected_agent:
            return

        screen_x, screen_y = self.camera.world_to_screen(cell.x, cell.y)
        radius = max(2, int(cell.radius * self.camera.zoom))

        if (0 <= screen_x <= SCREEN_WIDTH and
            0 <= screen_y <= SCREEN_HEIGHT and
            radius > 1):

            # Розміри HP бару
            bar_width = max(20, radius * 2)
            bar_height = max(3, radius // 4)
            bar_x = screen_x - bar_width // 2
            bar_y = screen_y - radius - bar_height - 5

            # Фон HP бару
            pygame.draw.rect(self.screen, (50, 50, 50),
                           (bar_x - 1, bar_y - 1, bar_width + 2, bar_height + 2))

            # Заповнення HP бару з кольоровим кодуванням
            fill_width = int(bar_width * hp_ratio)
            if hp_ratio > 0.6:
                hp_color = (0, 255, 0)  # Зелений
            elif hp_ratio > 0.3:
                hp_color = (255, 255, 0)  # Жовтий
            else:
                hp_color = (255, 0, 0)  # Червоний

            if fill_width > 0:
                pygame.draw.rect(self.screen, hp_color,
                               (bar_x, bar_y, fill_width, bar_height))

            # Контур HP бару
            pygame.draw.rect(self.screen, (200, 200, 200),
                           (bar_x, bar_y, bar_width, bar_height), 1)

    def _render_combat_effects(self, cell):
        """Рендерить бойові ефекти."""
        screen_x, screen_y = self.camera.world_to_screen(cell.x, cell.y)
        radius = max(2, int(cell.radius * self.camera.zoom))

        if (0 <= screen_x <= SCREEN_WIDTH and
            0 <= screen_y <= SCREEN_HEIGHT and
            radius > 1):

            # Ефект атаки (червоний спалах)
            if hasattr(cell, 'attack_cooldown_timer') and cell.attack_cooldown_timer > ATTACK_COOLDOWN - 0.3:
                # Показуємо спалах протягом 0.3 секунди після атаки
                flash_alpha = int(255 * (cell.attack_cooldown_timer - (ATTACK_COOLDOWN - 0.3)) / 0.3)
                flash_surface = pygame.Surface((radius * 4, radius * 4))
                flash_surface.set_alpha(flash_alpha)
                flash_surface.fill((255, 0, 0))
                self.screen.blit(flash_surface, (screen_x - radius * 2, screen_y - radius * 2))

            # Ефект захисту (синій щит)
            if hasattr(cell, 'escape_timer') and cell.escape_timer > 0:
                # Показуємо щит під час втечі
                shield_alpha = int(100 * (cell.escape_timer / ESCAPE_DURATION))
                shield_color = (0, 100, 255, shield_alpha)
                pygame.draw.circle(self.screen, shield_color[:3],
                                 (screen_x, screen_y), radius + 3, 2)

            # Іконки атаки/захисту біля агентів
            if hasattr(cell, 'is_carnivore') and cell.is_carnivore() and radius > 10:
                # Іконка атаки для м'ясоїдних (червоний трикутник)
                icon_size = max(3, radius // 3)
                icon_x = screen_x + radius + 5
                icon_y = screen_y - radius
                attack_points = [
                    (icon_x, icon_y - icon_size),
                    (icon_x - icon_size, icon_y + icon_size),
                    (icon_x + icon_size, icon_y + icon_size)
                ]
                pygame.draw.polygon(self.screen, (255, 0, 0), attack_points)

            elif hasattr(cell, 'is_herbivore') and cell.is_herbivore() and radius > 10:
                # Іконка захисту для травоїдних (синій щит)
                icon_size = max(3, radius // 3)
                icon_x = screen_x + radius + 5
                icon_y = screen_y - radius
                pygame.draw.circle(self.screen, (0, 100, 255),
                                 (icon_x, icon_y), icon_size, 2)

    def _render_ui(self, environment: Environment):
        """Рендерить користувацький інтерфейс."""
        stats = environment.get_generation_stats()
        # alive_cells = environment.get_alive_cells()  # Не використовується в цій функції

        # Фон для UI
        ui_surface = pygame.Surface((SCREEN_WIDTH, HUD_HEIGHT))
        ui_surface.set_alpha(180)
        ui_surface.fill((0, 0, 0))
        self.screen.blit(ui_surface, (0, 0))

        # Основна інформація
        y_offset = 10

        # Покоління та час
        gen_text = f"Покоління: {stats['generation']}"
        time_text = f"Час: {stats['time']:.1f}с"
        camera_mode = "Вільна" if self.camera.free_camera_mode else "Авто"
        camera_text = f"Камера: {camera_mode}"
        self._render_text(gen_text, 10, y_offset, self.font_medium, self.ui_accent_color)
        self._render_text(time_text, 200, y_offset, self.font_medium, self.ui_text_color)
        self._render_text(camera_text, 350, y_offset, self.font_medium, self.ui_accent_color)

        # Статистика агентів
        y_offset += 30
        alive_text = f"Живих: {stats['alive_count']}/{POPULATION_SIZE}"
        energy_text = f"Макс енергія: {stats['max_energy']:.1f}"
        self._render_text(alive_text, 10, y_offset, self.font_small, self.ui_text_color)
        self._render_text(energy_text, 150, y_offset, self.font_small, self.ui_text_color)

        # Статистика подій
        splits_text = f"Поділи: {stats['total_splits']}"
        absorb_text = f"Поглинання: {stats['total_absorptions']}"
        self._render_text(splits_text, 300, y_offset, self.font_small, self.ui_text_color)
        self._render_text(absorb_text, 450, y_offset, self.font_small, self.ui_text_color)

        # Керування
        y_offset += 25
        camera_mode = "Вільна" if self.camera.free_camera_mode else "Авто"
        follow_mode = " | F-Слідкувати" if self.selected_agent else ""
        controls_text = f"R-Перезапуск | V-Зір | P-Пауза | C-Камера({camera_mode}) | WASD-Рух{follow_mode} | ESC-Вихід"
        self._render_text(controls_text, 10, y_offset, self.font_small, (200, 200, 200))

    def _render_minimap(self, environment: Environment):
        """Рендерить міні-карту."""
        # Позиція міні-карти
        minimap_x = SCREEN_WIDTH - MINIMAP_SIZE - MINIMAP_MARGIN
        minimap_y = MINIMAP_MARGIN + HUD_HEIGHT

        # Фон міні-карти
        minimap_surface = pygame.Surface((MINIMAP_SIZE, MINIMAP_SIZE))
        minimap_surface.set_alpha(200)
        minimap_surface.fill((40, 40, 40))
        self.screen.blit(minimap_surface, (minimap_x, minimap_y))

        # Масштаб міні-карти
        scale_x = MINIMAP_SIZE / WORLD_WIDTH
        scale_y = MINIMAP_SIZE / WORLD_HEIGHT

        # Рендерим клітини на міні-карті
        for cell in environment.get_alive_cells():
            mini_x = int(cell.x * scale_x) + minimap_x
            mini_y = int(cell.y * scale_y) + minimap_y
            mini_radius = max(1, int(cell.radius * scale_x * 2))

            pygame.draw.circle(self.screen, cell.base_color,
                             (mini_x, mini_y), mini_radius)

        # Показуємо область камери
        cam_x = int(self.camera.x * scale_x) + minimap_x
        cam_y = int(self.camera.y * scale_y) + minimap_y
        cam_size = max(5, int(50 / self.camera.zoom))

        pygame.draw.rect(self.screen, (255, 255, 255),
                        (cam_x - cam_size//2, cam_y - cam_size//2, cam_size, cam_size), 2)

        # Контур міні-карти
        pygame.draw.rect(self.screen, (100, 100, 100),
                        (minimap_x, minimap_y, MINIMAP_SIZE, MINIMAP_SIZE), 2)

    def _render_agent_info(self, agent):
        """Рендерить детальну інформацію про вибраного агента."""
        # Позиція панелі (права частина екрану) - збільшено розміри
        panel_x = SCREEN_WIDTH - 350
        panel_y = HUD_HEIGHT + 10
        panel_width = 330
        panel_height = min(600, SCREEN_HEIGHT - panel_y - 20)  # Адаптивна висота

        # Фон панелі
        panel_surface = pygame.Surface((panel_width, panel_height))
        panel_surface.set_alpha(200)
        panel_surface.fill((20, 20, 20))
        self.screen.blit(panel_surface, (panel_x, panel_y))

        # Контур панелі
        pygame.draw.rect(self.screen, (100, 100, 100),
                        (panel_x, panel_y, panel_width, panel_height), 2)

        y_offset = panel_y + 10
        max_y = panel_y + panel_height - 20  # Максимальна Y позиція для тексту

        # Заголовок
        if y_offset < max_y:
            self._render_text("ІНФОРМАЦІЯ ПРО АГЕНТА", panel_x + 10, y_offset,
                             self.font_medium, self.ui_accent_color)
            y_offset += 35

        # Допоміжна функція для безпечного рендерингу тексту
        def safe_render_text(text, x, y, font, color, line_height=20):
            nonlocal y_offset, max_y
            if y_offset < max_y:
                self._render_text(text, x, y, font, color)
                y_offset += line_height
                return True
            return False

        # Основна інформація
        if not safe_render_text(f"Енергія: {agent.energy:.1f}", panel_x + 10, y_offset,
                               self.font_small, self.ui_text_color):
            return  # Виходимо якщо немає місця

        # Прогрес-бар енергії
        if y_offset + 30 < max_y:
            bar_width = 250  # Збільшено ширину
            bar_height = 10
            bar_x = panel_x + 10
            bar_y = y_offset + 5

            # Фон прогрес-бару
            pygame.draw.rect(self.screen, (50, 50, 50), (bar_x, bar_y, bar_width, bar_height))

            # Заповнення прогрес-бару
            energy_ratio = min(1.0, agent.energy / agent.max_energy)
            fill_width = int(bar_width * energy_ratio)
            color = (0, 255, 0) if energy_ratio > 0.5 else (255, 255, 0) if energy_ratio > 0.2 else (255, 0, 0)
            pygame.draw.rect(self.screen, color, (bar_x, bar_y, fill_width, bar_height))

            y_offset += 30

        # Фізичні характеристики
        safe_render_text(f"Радіус: {agent.radius:.1f}", panel_x + 10, y_offset,
                        self.font_small, self.ui_text_color, 18)

        speed = math.sqrt(agent.velocity_x**2 + agent.velocity_y**2) if hasattr(agent, 'velocity_x') else 0
        safe_render_text(f"Швидкість: {speed:.1f}", panel_x + 10, y_offset,
                        self.font_small, self.ui_text_color, 18)

        # Відображаємо віртуальний вік замість реального
        virtual_age_text = agent.get_virtual_age_display()
        safe_render_text(f"Вік: {virtual_age_text}", panel_x + 10, y_offset,
                        self.font_small, self.ui_text_color, 25)

        # Поточна ціль та стан
        goal_names = {
            AgentGoal.SURVIVAL: "Виживання",
            AgentGoal.REPRODUCTION: "Розмноження",
            AgentGoal.EXPLORATION: "Дослідження"
        }
        state_names = {
            AgentState.HUNGRY: "Голодний",
            AgentState.SATISFIED: "Задоволений",
            AgentState.FULL: "Ситий"
        }

        goal_name = goal_names.get(agent.current_goal, "Невідомо")
        state_name = state_names.get(agent.current_state, "Невідомо")
        goal_color = GOAL_COLORS.get(agent.current_goal, self.ui_accent_color)

        self._render_text(f"Ціль: {goal_name}", panel_x + 10, y_offset,
                         self.font_small, goal_color)
        y_offset += 18

        self._render_text(f"Стан: {state_name}", panel_x + 10, y_offset,
                         self.font_small, self.ui_text_color)
        y_offset += 25

        # Соціальна роль
        role_names = {
            SocialRole.WORKER: "Робітник",
            SocialRole.SOLDIER: "Солдат",
            SocialRole.QUEEN: "Королева",
            SocialRole.DRONE: "Розвідник"
        }

        if hasattr(agent, 'social_role'):
            role_name = role_names.get(agent.social_role, "Невідомо")
            role_color = ROLE_COLORS.get(agent.social_role, self.ui_accent_color)
            self._render_text(f"Роль: {role_name}", panel_x + 10, y_offset,
                             self.font_small, role_color)
            y_offset += 18

        # Інвентар
        if hasattr(agent, 'inventory'):
            inventory_display = agent.get_inventory_display()
            self._render_text(f"Інвентар: {inventory_display}", panel_x + 10, y_offset,
                             self.font_small, self.ui_text_color)
            y_offset += 25

        # Тип харчування
        diet_type = agent.get_diet_type()
        self._render_text(f"Тип харчування: {diet_type}", panel_x + 10, y_offset,
                         self.font_small, self.ui_accent_color)
        y_offset += 25

        # HP та бойова статистика
        if hasattr(agent, 'current_hp'):
            hp_ratio = agent.get_hp_ratio()
            self._render_text(f"HP: {agent.current_hp:.1f}/{agent.max_hp:.1f}", panel_x + 10, y_offset,
                             self.font_small, self.ui_text_color)

            # HP бар
            hp_bar_width = 150
            hp_bar_height = 8
            hp_bar_x = panel_x + 120
            hp_bar_y = y_offset + 2

            # Фон HP бару
            pygame.draw.rect(self.screen, (50, 50, 50), (hp_bar_x, hp_bar_y, hp_bar_width, hp_bar_height))

            # Заповнення HP бару
            hp_fill_width = int(hp_bar_width * hp_ratio)
            if hp_ratio > 0.6:
                hp_color = (0, 255, 0)
            elif hp_ratio > 0.3:
                hp_color = (255, 255, 0)
            else:
                hp_color = (255, 0, 0)

            if hp_fill_width > 0:
                pygame.draw.rect(self.screen, hp_color, (hp_bar_x, hp_bar_y, hp_fill_width, hp_bar_height))

            pygame.draw.rect(self.screen, (200, 200, 200), (hp_bar_x, hp_bar_y, hp_bar_width, hp_bar_height), 1)
            y_offset += 25

            # Бойова статистика
            if hasattr(agent, 'damage_dealt'):
                self._render_text(f"Урон завдано: {agent.damage_dealt:.1f}", panel_x + 10, y_offset,
                                 self.font_small, (255, 100, 100))
                y_offset += 18

                self._render_text(f"Урон отримано: {agent.damage_received:.1f}", panel_x + 10, y_offset,
                                 self.font_small, (255, 200, 100))
                y_offset += 18

                self._render_text(f"Вбивств: {agent.kills}", panel_x + 10, y_offset,
                                 self.font_small, (255, 0, 0))
                y_offset += 18

                self._render_text(f"Боїв: {agent.combat_encounters}", panel_x + 10, y_offset,
                                 self.font_small, (200, 200, 200))
                y_offset += 25

        # Система заохочень та покарань
        if hasattr(agent, 'total_rewards') and hasattr(agent, 'total_penalties'):
            if y_offset < max_y:
                self._render_text("ЗАОХОЧЕННЯ ТА ПОКАРАННЯ:", panel_x + 10, y_offset,
                                 self.font_small, self.ui_accent_color)
                y_offset += 20

            # Загальна статистика
            if y_offset < max_y:
                self._render_text(f"Заохочень: +{agent.total_rewards:.1f}", panel_x + 10, y_offset,
                                 self.font_small, (0, 255, 0))
                y_offset += 18

            if y_offset < max_y:
                self._render_text(f"Покарань: -{agent.total_penalties:.1f}", panel_x + 10, y_offset,
                                 self.font_small, (255, 100, 100))
                y_offset += 18

            # Співвідношення
            if y_offset < max_y:
                ratio = agent.get_reward_penalty_ratio()
                ratio_text = f"∞" if ratio == float('inf') else f"{ratio:.2f}"
                ratio_color = (0, 255, 0) if ratio > 1.5 else (255, 255, 0) if ratio > 0.8 else (255, 100, 100)
                self._render_text(f"Співвідношення: {ratio_text}", panel_x + 10, y_offset,
                                 self.font_small, ratio_color)
                y_offset += 18

            # Модифікатори фітнесу
            if y_offset < max_y and hasattr(agent, 'fitness_modifiers'):
                modifier_percent = agent.fitness_modifiers * 100
                modifier_color = (0, 255, 0) if modifier_percent > 0 else (255, 100, 100) if modifier_percent < 0 else (200, 200, 200)
                self._render_text(f"Модифікатор фітнесу: {modifier_percent:+.1f}%", panel_x + 10, y_offset,
                                 self.font_small, modifier_color)
                y_offset += 25

        # Статистика споживання
        diet_percentages = agent.get_diet_percentages()
        self._render_text("Споживання:", panel_x + 10, y_offset,
                         self.font_small, self.ui_text_color)
        y_offset += 20

        self._render_text(f"  Рослинна їжа: {agent.plant_food_consumed} ({diet_percentages['рослинна']:.1f}%)",
                         panel_x + 10, y_offset, self.font_small, (0, 255, 0))
        y_offset += 18

        self._render_text(f"  Білкова їжа: {agent.protein_food_consumed} ({diet_percentages['білкова']:.1f}%)",
                         panel_x + 10, y_offset, self.font_small, (255, 100, 100))
        y_offset += 18

        self._render_text(f"  Отрута: {agent.poison_consumed}",
                         panel_x + 10, y_offset, self.font_small, (255, 0, 255))
        y_offset += 30

        # Топ-5 генів
        self._render_text("Топ-5 генів:", panel_x + 10, y_offset,
                         self.font_small, self.ui_accent_color)
        y_offset += 20

        # Сортуємо гени за значенням
        sorted_genes = sorted(agent.genes.items(), key=lambda x: x[1], reverse=True)[:5]

        for gene_name, gene_value in sorted_genes:
            display_name = gene_name.replace('_', ' ').title()
            self._render_text(f"  {display_name}: {gene_value:.2f}",
                             panel_x + 10, y_offset, self.font_small, self.ui_text_color)
            y_offset += 18

    def _render_restart_confirmation(self):
        """Рендерить діалог підтвердження перезапуску."""
        # Напівпрозорий фон
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(150)
        overlay.fill((0, 0, 0))
        self.screen.blit(overlay, (0, 0))

        # Діалогове вікно
        dialog_width = 400
        dialog_height = 150
        dialog_x = (SCREEN_WIDTH - dialog_width) // 2
        dialog_y = (SCREEN_HEIGHT - dialog_height) // 2

        # Фон діалогу
        pygame.draw.rect(self.screen, (40, 40, 40),
                        (dialog_x, dialog_y, dialog_width, dialog_height))
        pygame.draw.rect(self.screen, (100, 100, 100),
                        (dialog_x, dialog_y, dialog_width, dialog_height), 3)

        # Текст
        self._render_text("Перезапустити покоління?",
                         dialog_x + 20, dialog_y + 30,
                         self.font_medium, self.ui_text_color)

        self._render_text("Y - Так    N - Ні    ESC - Скасувати",
                         dialog_x + 20, dialog_y + 80,
                         self.font_small, (200, 200, 200))

    def _render_pause_overlay(self):
        """Рендерить оверлей паузи."""
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        self.screen.blit(overlay, (0, 0))

        pause_text = "ПАУЗА"
        text_surface = self.font_large.render(pause_text, True, (255, 255, 255))
        text_rect = text_surface.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
        self.screen.blit(text_surface, text_rect)

    def _render_text(self, text: str, x: int, y: int, font: pygame.font.Font, color: Tuple[int, int, int]):
        """Рендерить текст."""
        text_surface = font.render(text, True, color)
        self.screen.blit(text_surface, (x, y))

    def cleanup(self):
        """Очищує ресурси."""
        pygame.quit()
