# 🎮 Інструкції для запуску Agar.io C<PERSON> з ШІ-агентами

## 🚀 Швидкий старт

### 1. Встановлення залежностей
```bash
pip install pygame
```

### 2. Запуск гри
```bash
python main.py
```

### 3. Тестування функцій (опціонально)
```bash
python test_demo.py
```

## 🎯 Що ви побачите

### Головне вікно гри (1200x800 пікселів)
- **Ігрове поле**: 2000x2000 пікселів з сіткою
- **50 ШІ-агентів**: кольорові клітини з різними стратегіями
- **250 одиниць їжі**: зелені кружечки для збільшення енергії
- **80 одиниць отрути**: червоні кружечки що зменшують енергію

### HUD (верхня панель)
- Номер покоління та час симуляції
- Кількість живих агентів
- Максимальна енергія в поколінні
- Статистика поділів та поглинань

### Міні-карта (правий верхній кут)
- Огляд всього світу
- Позиції всіх агентів
- Область камери (білий квадрат)

### Система камери
- **Вільний режим (за замовчуванням)**: повне керування камерою клавішами WASD/стрілки
- **Автоматичний режим**: камера слідкує за найбільшою клітиною
- **Плавні переходи**: м'яка анімація руху та зуму
- **Обмеження**: камера не виходить за межі світу
- **Швидкий рух агентів**: ~60 пікселів/секунду для кращої видимості

## 🎮 Керування

| Клавіша | Дія |
|---------|-----|
| **R** | Перезапуск поточного покоління |
| **V** | Увімкнути/вимкнути візуалізацію променів зору |
| **P** | Пауза/продовження симуляції |
| **C** | Переключення режиму камери (авто/вільна) |
| **WASD** або **Стрілки** | Рух вільної камери |
| **ESC** | Вихід з програми |
| **Колесо миші** | Зум камери (0.5x - 3.0x) |

## 🧠 Як працює ШІ

### Система зору
Кожен агент має **8 променів зору** під кутами:
- 0°, 45°, 90°, 135°, 180°, 225°, 270°, 315°

Промені виявляють:
- 🟢 **Їжу** - збільшує енергію на 15 одиниць
- 🔴 **Отруту** - зменшує енергію на 25 одиниць
- 🔵 **Інші клітини** - можна поглинути або бути поглинутим
- ⬜ **Стіни** - межі світу

### Поведінка агентів
Агенти приймають рішення на основі **14 генів**:
- Притягнення до їжі
- Уникнення отрути та загроз
- Дослідження нових областей
- Агресивність та обережність

### Еволюція
Кожні **60 секунд** або при смерті всіх агентів:
1. Оцінюється фітнес (енергія + час виживання)
2. Найкращі 20% стають батьками
3. Створюється нове покоління з мутаціями
4. Найкращі гени зберігаються в `best_genes.json`

## 📊 Файли даних

### `best_genes.json`
Найкращі гени з усіх поколінь:
```json
{
  "genes": {
    "food_attraction": 1.39,
    "poison_avoidance": 1.99,
    ...
  },
  "fitness": 0.92,
  "generation": 5
}
```

### `generation_stats.csv`
Статистика кожного покоління:
```csv
Generation,Time,Alive_Count,Max_Energy,Total_Splits,...
1,60.0,15,245.3,12,...
2,45.2,8,189.7,8,...
```

## 🔧 Налаштування

Основні параметри в `config.py`:

### Популяція
```python
POPULATION_SIZE = 50        # Кількість агентів
GENERATION_TIME = 60        # Час покоління (секунди)
ELITE_RATIO = 0.2          # Частка еліти (20%)
MUTATION_RATE = 0.3        # Ймовірність мутації (30%)
```

### Світ
```python
WORLD_WIDTH = 2000         # Ширина світу
WORLD_HEIGHT = 2000        # Висота світу
FOOD_COUNT = 250           # Кількість їжі
POISON_COUNT = 80          # Кількість отрути
```

### Агенти
```python
AGENT_START_ENERGY = 100   # Початкова енергія
VISION_RANGE_MULTIPLIER = 4 # Дальність зору
AGENT_SPLIT_THRESHOLD = 200 # Енергія для поділу
```

## 🎨 Візуальні ефекти

### Стиль .io ігор
- **Градієнтні клітини**: від світлого центру до темних країв
- **Плавна анімація**: інтерполяція руху та зуму
- **Динамічна камера**: слідкування за найбільшою клітиною
- **Блики та тіні**: реалістичні візуальні ефекти

### Промені зору (клавіша V)
- Зелені лінії до їжі
- Червоні лінії до отрути
- Жовті лінії до інших клітин
- Сірі лінії до стін

## 🏆 Цілі еволюції

Агенти еволюціонують для:
- **Максимізації енергії** - ефективний пошук їжі
- **Виживання** - уникнення загроз та отрути
- **Розмноження** - поділ при достатній енергії
- **Адаптації** - пристосування до середовища

## 🐛 Усунення проблем

### Гра не запускається
```bash
# Перевірте версію Python (потрібен 3.7+)
python --version

# Встановіть pygame
pip install pygame

# Запустіть тест
python test_demo.py
```

### Низька продуктивність
- Зменшіть `POPULATION_SIZE` в `config.py`
- Зменшіть `FOOD_COUNT` та `POISON_COUNT`
- Закрийте інші програми

### Помилки збереження
- Перевірте права доступу до папки
- Переконайтеся що є вільне місце на диску

## 📈 Спостереження за еволюцією

### Перші покоління (1-5)
- Хаотичний рух
- Багато смертей від отрути
- Низька ефективність пошуку їжі

### Середні покоління (5-20)
- Покращення уникнення отрути
- Більш цілеспрямований рух
- Перші успішні поділи

### Пізні покоління (20+)
- Ефективні стратегії виживання
- Складна поведінка
- Високі показники енергії

## 🎯 Експерименти

Спробуйте змінити параметри та спостерігайте:
- Збільшіть `MUTATION_RATE` для швидшої еволюції
- Зменшіть `FOOD_COUNT` для жорсткішого відбору
- Змініть `VISION_RANGE_MULTIPLIER` для іншої стратегії

---

**Насолоджуйтесь спостереженням за еволюцією штучного інтелекту! 🧬🤖**
