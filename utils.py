# Допоміжні функції для гри Agar.io Clone
import math
import random
from typing import Tuple, List, Optional

def distance(pos1: <PERSON><PERSON>[float, float], pos2: Tuple[float, float]) -> float:
    """Обчислює відстань між двома точками."""
    # Захисна перевірка від від'ємних значень під sqrt
    distance_squared = (pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2
    return math.sqrt(max(0.0, distance_squared))

def normalize_vector(vector: Tuple[float, float]) -> Tuple[float, float]:
    """Нормалізує вектор до одиничної довжини."""
    # Захисна перевірка від від'ємних значень під sqrt
    magnitude_squared = vector[0]**2 + vector[1]**2
    magnitude = math.sqrt(max(0.0, magnitude_squared))
    if magnitude == 0:
        return (0, 0)
    return (vector[0] / magnitude, vector[1] / magnitude)

def clamp(value: float, min_val: float, max_val: float) -> float:
    """Обмежує значення в межах [min_val, max_val]."""
    return max(min_val, min(value, max_val))

def lerp(a: float, b: float, t: float) -> float:
    """Лінійна інтерполяція між a та b."""
    return a + (b - a) * t

def angle_to_vector(angle: float) -> Tuple[float, float]:
    """Конвертує кут у радіанах у одиничний вектор."""
    return (math.cos(angle), math.sin(angle))

def vector_to_angle(vector: Tuple[float, float]) -> float:
    """Конвертує вектор у кут у радіанах."""
    return math.atan2(vector[1], vector[0])

def rotate_point(point: Tuple[float, float], center: Tuple[float, float], angle: float) -> Tuple[float, float]:
    """Повертає точку навколо центру на заданий кут."""
    cos_a = math.cos(angle)
    sin_a = math.sin(angle)

    # Переміщуємо точку відносно центру
    x = point[0] - center[0]
    y = point[1] - center[1]

    # Повертаємо
    new_x = x * cos_a - y * sin_a
    new_y = x * sin_a + y * cos_a

    # Повертаємо до оригінальної системи координат
    return (new_x + center[0], new_y + center[1])

def circles_collide(pos1: Tuple[float, float], radius1: float,
                   pos2: Tuple[float, float], radius2: float) -> bool:
    """Перевіряє колізію між двома кругами."""
    return distance(pos1, pos2) < (radius1 + radius2)

def point_in_circle(point: Tuple[float, float], center: Tuple[float, float], radius: float) -> bool:
    """Перевіряє чи знаходиться точка всередині кола."""
    return distance(point, center) <= radius

def line_circle_intersection(line_start: Tuple[float, float], line_end: Tuple[float, float],
                           circle_center: Tuple[float, float], circle_radius: float) -> Optional[Tuple[float, float]]:
    """Знаходить точку перетину лінії з колом (найближчу до початку лінії)."""
    # Вектор лінії
    dx = line_end[0] - line_start[0]
    dy = line_end[1] - line_start[1]

    # Вектор від початку лінії до центру кола
    fx = line_start[0] - circle_center[0]
    fy = line_start[1] - circle_center[1]

    # Квадратне рівняння для перетину
    a = dx * dx + dy * dy
    b = 2 * (fx * dx + fy * dy)
    c = (fx * fx + fy * fy) - circle_radius * circle_radius

    # Захист від нульової лінії (a = 0)
    if a == 0:
        return None  # Нульова лінія

    discriminant = b * b - 4 * a * c

    if discriminant < 0:
        return None  # Немає перетину

    # Знаходимо найближчу точку перетину
    # Захисна перевірка від від'ємних значень під sqrt
    discriminant = math.sqrt(max(0.0, discriminant))
    t1 = (-b - discriminant) / (2 * a)
    t2 = (-b + discriminant) / (2 * a)

    # Вибираємо найближчу точку в межах лінії
    t = None
    if 0 <= t1 <= 1:
        t = t1
    elif 0 <= t2 <= 1:
        t = t2

    if t is not None:
        return (line_start[0] + t * dx, line_start[1] + t * dy)

    return None

def random_position_in_world(world_width: float, world_height: float,
                           margin: float = 50) -> Tuple[float, float]:
    """Генерує випадкову позицію у світі з відступом від країв."""
    x = random.uniform(margin, world_width - margin)
    y = random.uniform(margin, world_height - margin)
    return (x, y)

def wrap_position(pos: Tuple[float, float], world_width: float, world_height: float) -> Tuple[float, float]:
    """Обгортає позицію в межах світу (для торичної топології)."""
    x = pos[0] % world_width
    y = pos[1] % world_height
    return (x, y)

def clamp_position(pos: Tuple[float, float], radius: float,
                  world_width: float, world_height: float) -> Tuple[float, float]:
    """Обмежує позицію в межах світу з урахуванням радіуса об'єкта."""
    x = clamp(pos[0], radius, world_width - radius)
    y = clamp(pos[1], radius, world_height - radius)
    return (x, y)

def energy_to_radius(energy: float, scale: float = 1.5) -> float:
    """Конвертує енергію в радіус клітини."""
    # Захисна перевірка від від'ємних значень
    safe_energy = max(0.1, energy)
    return math.sqrt(safe_energy / math.pi) * scale

def radius_to_energy(radius: float, scale: float = 1.5) -> float:
    """Конвертує радіус клітини в енергію."""
    return (radius / scale) ** 2 * math.pi

def calculate_speed(radius: float, base_speed: float, base_radius: float = 10) -> float:
    """Обчислює базову швидкість незалежно від розміру клітини.

    Args:
        radius: Радіус клітини (не впливає на швидкість)
        base_speed: Базова швидкість
        base_radius: Базовий радіус (не використовується)

    Returns:
        float: Базова швидкість для всіх агентів
    """
    return base_speed

def smooth_step(t: float) -> float:
    """Плавна інтерполяція (S-крива)."""
    return t * t * (3 - 2 * t)

def create_gradient_color(center_color: Tuple[int, int, int],
                         edge_color: Tuple[int, int, int],
                         t: float) -> Tuple[int, int, int]:
    """Створює градієнтний колір між центром та краєм."""
    r = int(lerp(center_color[0], edge_color[0], t))
    g = int(lerp(center_color[1], edge_color[1], t))
    b = int(lerp(center_color[2], edge_color[2], t))
    return (clamp(r, 0, 255), clamp(g, 0, 255), clamp(b, 0, 255))
