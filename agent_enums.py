# Enum класи для агентів
from enum import Enum

class AgentGoal(Enum):
    """Цілі агентів."""
    SURVIVAL = "survival"      # Пошук їжі при енергії < 50%
    REPRODUCTION = "reproduction"  # Поділ при енергії > 150%
    EXPLORATION = "exploration"    # Дослідження при середній енергії

class AgentState(Enum):
    """Стани агентів за рівнем енергії."""
    HUNGRY = "hungry"      # Енергія < 30%
    SATISFIED = "satisfied"  # Енергія 30-70%
    FULL = "full"          # Енергія > 70%

class LifeStage(Enum):
    """Етапи життя агентів."""
    YOUTH = "youth"    # 0-7 днів
    ADULT = "adult"    # 7-30 днів
    ELDER = "elder"    # 30+ днів

class PerformanceMode(Enum):
    """Режими продуктивності."""
    HIGH = "high"      # 50 агентів
    MEDIUM = "medium"  # 30 агентів
    LOW = "low"        # 15 агентів

class SocialRole(Enum):
    """Соціальні ролі агентів."""
    WORKER = "worker"      # Збирач їжі, 70% популяції
    SOLDIER = "soldier"    # Захисник, 15% популяції
    QUEEN = "queen"        # Репродуктор, 5% популяції
    DRONE = "drone"        # Розвідник, 10% популяції

# Кольори для візуального відображення цілей
GOAL_COLORS = {
    AgentGoal.SURVIVAL: (0, 255, 0),      # Зелений
    AgentGoal.REPRODUCTION: (0, 100, 255), # Синій
    AgentGoal.EXPLORATION: (255, 255, 0)   # Жовтий
}

# Кольори для станів агентів
STATE_COLORS = {
    AgentState.HUNGRY: (255, 100, 100),    # Червонуватий
    AgentState.SATISFIED: (100, 255, 100), # Зеленуватий
    AgentState.FULL: (100, 100, 255)       # Синюватий
}

# Кольори для соціальних ролей
ROLE_COLORS = {
    SocialRole.WORKER: (150, 150, 150),    # Сірий
    SocialRole.SOLDIER: (255, 100, 100),   # Червоний
    SocialRole.QUEEN: (255, 215, 0),       # Золотий
    SocialRole.DRONE: (100, 150, 255)      # Синій
}

# Налаштування популяції для різних режимів продуктивності
PERFORMANCE_SETTINGS = {
    PerformanceMode.HIGH: {
        'population_size': 50,
        'vision_update_freq': 3,
        'collision_optimization': True
    },
    PerformanceMode.MEDIUM: {
        'population_size': 30,
        'vision_update_freq': 2,
        'collision_optimization': True
    },
    PerformanceMode.LOW: {
        'population_size': 15,
        'vision_update_freq': 1,
        'collision_optimization': False
    }
}
