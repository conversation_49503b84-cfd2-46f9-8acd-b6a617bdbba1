# Agar.io Clone з ШІ-агентами

Повнофункціональний клон гри agar.io з ШІ-агентами замість гравців, використовуючи Python та pygame для візуалізації. Проект включає модульну архітектуру з можливістю еволюційного навчання.

## Особливості

### ШІ-Агенти
- **Променева система зору**: 8 променів під фіксованими кутами (0°, 45°, 90°, 135°, 180°, 225°, 270°, 315°)
- **Збільшена дальність зору**: 8 радіусів клітини (було 4) для кращого дослідження світу
- **Інтелектуальна поведінка**: активне дослідження світу, міграційні інстинкти, пошук їжі
- **Система пам'яті**: відстеження часу без їжі, "нудьга" в одній області, направлене блукання
- **Динамічні характеристики**: швидкість ~60 пікселів/сек, залежить від розміру
- **Поділ клітин**: автоматичний поділ при досягненні достатньої енергії

### Еволюційне навчання
- **Генетичний алгоритм**: селекція найкращих 20%, схрещування та мутації
- **Збереження прогресу**: найкращі гени зберігаються між сесіями
- **Статистика**: детальне відстеження прогресу поколінь
- **Ручний перезапуск**: тільки за командою користувача або при смерті всіх агентів

### Система харчування та типи їжі
- **Рослинна їжа (зелена)**: дає базову енергію 15 одиниць, безпечна для всіх
- **Білкова їжа (червона)**: дає 25 енергії м'ясоїдним, 5 енергії травоїдним
- **Отрута (фіолетова)**: забирає 25 енергії при споживанні
- **Співвідношення**: 60% рослинної, 40% білкової їжі
- **Генетичні типи**: травоїдні, м'ясоїдні, всеїдні агенти з різними стратегіями

### Візуалізація у стилі .io ігор
- **Градієнтні ефекти**: клітини з плавними переходами кольорів
- **Гнучка система камери**: автоматичне слідкування або вільне керування
- **Міні-карта**: огляд всього світу в реальному часі
- **HUD**: статистика покоління, керування, прогрес еволюції
- **Інтерактивна панель агента**: детальна інформація про вибраного агента
- **Кольорове кодування**: травоїдні (зеленуваті), м'ясоїдні (червонуваті), всеїдні (базові кольори)

### Система камери
- **Автоматичний режим**: камера автоматично слідкує за найбільшою клітиною
- **Вільний режим**: повне керування камерою клавішами WASD/стрілки
- **Плавні переходи**: м'яка анімація руху та зуму
- **Інтелектуальні обмеження**: камера не виходить за межі світу

## Встановлення

1. **Клонуйте репозиторій**:
```bash
git clone <repository-url>
cd AI-Cell
```

2. **Встановіть залежності**:
```bash
pip install -r requirements.txt
```

3. **Запустіть гру**:
```bash
python main.py
```

## Керування

### Основні клавіші
- **R** - Показати діалог перезапуску покоління
- **Y** - Підтвердити перезапуск (після натискання R)
- **N** - Скасувати перезапуск (після натискання R)
- **V** - Увімкнути/вимкнути візуалізацію променів зору
- **P** - Пауза/продовження симуляції
- **C** - Переключення режиму камери (авто/вільна)
- **F** - Слідкувати камерою за вибраним агентом
- **WASD** або **Стрілки** - Рух вільної камери
- **ESC** - Вихід з програми або скасування діалогів
- **Колесо миші** - Зум камери

### Інтерактивність
- **Лівий клік миші** - Вибрати агента для детального перегляду
- **Клік по порожньому місцю** - Скасувати вибір агента

## Архітектура проекту

```
AI Cell/
├── main.py              # Точка входу з основним циклом гри
├── cell.py              # Клас Cell з логікою ШІ-агентів
├── environment.py       # Клас Environment для управління світом
├── evolution.py         # Система еволюційного навчання
├── visualization.py     # Pygame візуалізація та інтерфейс
├── config.py           # Налаштування гри
├── utils.py            # Допоміжні функції
├── requirements.txt    # Залежності Python
└── README.md          # Документація
```

## Налаштування

Основні параметри можна змінити в `config.py`:

### Світ гри
- `WORLD_WIDTH/HEIGHT` - розміри світу (2000x2000)
- `FOOD_COUNT` - кількість їжі (450, збільшено з 250)
- `POISON_COUNT` - кількість отрути (60, зменшено з 80)
- `FOOD_RESPAWN_DELAY` - респавн їжі (20 тіків, прискорено з 30)

### ШІ-агенти
- `POPULATION_SIZE` - розмір популяції (50)
- `AGENT_START_ENERGY` - початкова енергія (100)
- `AGENT_BASE_SPEED` - базова швидкість (50 пікселів/сек)
- `VISION_RANGE_MULTIPLIER` - дальність зору (8 радіусів)

### Еволюція
- `ELITE_RATIO` - частка еліти (20%)
- `MUTATION_RATE` - ймовірність мутації (30%)
- Автоматичний перезапуск за часом **видалено** - тільки ручний або при смерті всіх

## Інтерактивні функції

### Вибір та спостереження за агентами
- **Клік по агенту** - вибір для детального перегляду
- **Інформаційна панель** - показує енергію, радіус, швидкість, вік агента
- **Статистика споживання** - кількість з'їденої рослинної/білкової їжі та отрути
- **Тип харчування** - травоїдний/м'ясоїдний/всеїдний з відсотковим співвідношенням
- **Топ-5 генів** - найважливіші генетичні параметри агента
- **Слідкування камерою** - автоматичне слідкування за вибраним агентом (клавіша F)
- **Візуальне виділення** - білий контур навколо вибраного агента

### Система перезапуску
- **Діалогове підтвердження** - попередження перед перезапуском покоління
- **Безпечний перезапуск** - неможливо випадково перезапустити покоління
- **Збереження прогресу** - всі дані зберігаються перед перезапуском

## Як працює ШІ

### Система зору
Кожен агент випускає 8 променів у фіксованих напрямках. Промені повертають інформацію про:
- Тип об'єкта (їжа, отрута, клітина, стіна)
- Відстань до об'єкта

### Прийняття рішень
Агент аналізує дані зору та приймає рішення на основі свого генома:
- **Притягнення до їжі** - рух до найближчої їжі
- **Уникнення отрути** - втеча від небезпечних об'єктів
- **Уникнення загроз** - втеча від більших клітин
- **Дослідження** - випадковий рух для пошуку нових областей

### Геном агента
Кожен агент має 23 гени, що контролюють:
- **Сприйняття об'єктів**: ваги для їжі, отрути, клітин, стін
- **Поведінку на відстанях**: близькі, середні, далекі об'єкти
- **Енергетичний стан**: паніка при низькій енергії, впевненість при високій
- **Дослідження світу**: схильність до дослідження, сила блукання, міграція
- **Тип харчування**: травоїдний (0.0) ↔ м'ясоїдний (1.0)
- **Ефективність їжі**: окремо для рослинної та білкової їжі
- **Поведінкові стратегії**: агресивність полювання, захист травоїдних

## Еволюційний процес

1. **Оцінка фітнесу**: на основі максимальної енергії та часу виживання
2. **Селекція**: найкращі 20% агентів стають батьками
3. **Схрещування**: комбінування генів від двох батьків
4. **Мутація**: випадкові зміни генів з ймовірністю 30%
5. **Нове покоління**: 50 нових агентів з покращеними генами

## Файли даних

- `best_genes.json` - найкращі гени з усіх поколінь
- `generation_stats.csv` - статистика кожного покоління

## Системні вимоги

- Python 3.7+
- pygame 2.1.0+
- 4 ГБ RAM (рекомендовано)
- Відеокарта з підтримкою OpenGL

## Можливі покращення

- Додавання нейронних мереж замість простих генів
- Більш складна система зору (змінна кількість променів)
- Кооперативна поведінка між агентами
- Різні типи їжі з різними властивостями
- Збереження та відтворення найкращих симуляцій

## Ліцензія

MIT License - дивіться файл LICENSE для деталей.
